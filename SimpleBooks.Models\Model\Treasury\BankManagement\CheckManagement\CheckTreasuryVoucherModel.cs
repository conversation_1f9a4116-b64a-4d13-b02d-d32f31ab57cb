﻿namespace SimpleBooks.Models.Model.Treasury.BankManagement.CheckManagement
{
    [Table("CheckTreasuryVoucher")]
    [TypeDescriptionProvider(typeof(BaseTypeDescriptionProvider<CheckTreasuryVoucherModel>))]
    public partial class CheckTreasuryVoucherModel : TreasuryVoucherModel
    {
        [CustomRequired]
        [DisplayName("Check Number")]
        public string CheckNumber { get; set; }
        [CustomRequired]
        [DisplayName("Due Date")]
        [Column(TypeName = "Date"), DataType(DataType.Date)]
        public DateTime DueDate { get; set; }
        [CustomRequired]
        [DisplayName("Issuer Name")]
        public string IssuerName { get; set; } = string.Empty; // For received checks
        [CustomRequired]
        [DisplayName("Bearer Name")]
        public string BearerName { get; set; } = string.Empty; // For issued checks
        [CustomRequired]
        [DisplayName("Check Status")]
        public Ulid CheckStatusId { get; set; }
        public virtual CheckStatusModel? CheckStatus { get; set; }

        [CustomRequired]
        [DisplayName("Check Vault")]
        public Ulid CheckVaultId { get; set; }
        public virtual CheckVaultModel? CheckVault { get; set; }

        [CustomRequired]
        [DisplayName("Check Vault Location")]
        public Ulid CheckVaultLocationId { get; set; }
        public virtual CheckVaultLocationModel? CheckVaultLocation { get; set; }

        [DisplayName("Check Deposit")]
        public Ulid? CheckDepositId { get; set; }
        public virtual CheckDepositModel? CheckDeposit { get; set; }

        [CustomRequired]
        [DisplayName("Check Status Histories")]
        public virtual ICollection<CheckStatusHistoryModel> CheckStatusHistories { get; set; } = new List<CheckStatusHistoryModel>();
        [DisplayName("Check Collections")]
        public virtual ICollection<CheckCollectionModel> CheckCollections { get; set; } = new List<CheckCollectionModel>();
        [DisplayName("Check Rejects")]
        public virtual ICollection<CheckRejectModel> CheckRejects { get; set; } = new List<CheckRejectModel>();
    }
}
