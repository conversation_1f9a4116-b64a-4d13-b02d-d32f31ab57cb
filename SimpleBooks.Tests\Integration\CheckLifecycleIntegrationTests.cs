using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;
using SimpleBooks.Models.Domain.Treasury.CheckManagement;
using SimpleBooks.Models.Model.Treasury.BankManagement.CheckManagement;
using SimpleBooks.Repositories.EF.Factory.Contexts;
using SimpleBooks.Services.Server.Business.Treasury.BankManagement.CheckManagement;
using Xunit;

namespace SimpleBooks.Tests.Integration
{
    /// <summary>
    /// Integration tests for the refactored check lifecycle system
    /// </summary>
    public class CheckLifecycleIntegrationTests : IDisposable
    {
        private readonly ApplicationDBContext _context;
        private readonly ICheckLifecycleService _checkLifecycleService;
        private readonly IServiceProvider _serviceProvider;

        public CheckLifecycleIntegrationTests()
        {
            // Setup in-memory database for testing
            var services = new ServiceCollection();
            services.AddDbContext<ApplicationDBContext>(options =>
                options.UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString()));

            // Register services (this would normally be done in DI container)
            services.AddScoped<ICheckLifecycleService, CheckLifecycleService>();
            // Add other required services...

            _serviceProvider = services.BuildServiceProvider();
            _context = _serviceProvider.GetRequiredService<ApplicationDBContext>();
            _checkLifecycleService = _serviceProvider.GetRequiredService<ICheckLifecycleService>();

            // Seed test data
            SeedTestData();
        }

        [Fact]
        public async Task CompleteCheckLifecycle_ShouldWorkCorrectly()
        {
            // Arrange
            var checkIds = await CreateTestChecks(3);
            var deposit = CreateTestDeposit();

            // Act & Assert - Deposit checks
            var depositResult = await _checkLifecycleService.DepositChecksAsync(checkIds, deposit);
            Assert.True(depositResult.IsSuccess);
            Assert.Equal(3, depositResult.Data.Count);
            Assert.All(depositResult.Data, check => Assert.Equal(CheckStatus.Deposited, check.Status));

            // Act & Assert - Collect first check
            var collection = CreateTestCollection(deposit.Id, checkIds[0]);
            var collectResult = await _checkLifecycleService.CollectCheckAsync(checkIds[0], collection);
            Assert.True(collectResult.IsSuccess);
            Assert.Equal(CheckStatus.Cleared, collectResult.Data.Status);

            // Act & Assert - Reject second check
            var rejection = CreateTestRejection(deposit.Id, checkIds[1]);
            var rejectResult = await _checkLifecycleService.RejectCheckAsync(checkIds[1], rejection);
            Assert.True(rejectResult.IsSuccess);
            Assert.Equal(CheckStatus.Rejected, rejectResult.Data.Status);

            // Act & Assert - Remove third check from deposit
            var removeResult = await _checkLifecycleService.RemoveCheckFromDepositAsync(checkIds[2], deposit.Id, "Test removal");
            Assert.True(removeResult.IsSuccess);
            Assert.Equal(CheckStatus.Received, removeResult.Data.Status);
            Assert.Null(removeResult.Data.DepositId);
        }

        [Fact]
        public async Task DepositChecks_WithInvalidStatus_ShouldFail()
        {
            // Arrange
            var checkIds = await CreateTestChecks(1);
            var deposit = CreateTestDeposit();

            // First deposit the check
            await _checkLifecycleService.DepositChecksAsync(checkIds, deposit);

            // Try to deposit again (should fail)
            var secondDeposit = CreateTestDeposit();
            
            // Act
            var result = await _checkLifecycleService.DepositChecksAsync(checkIds, secondDeposit);

            // Assert
            Assert.False(result.IsSuccess);
            Assert.Contains("already deposited", result.Message);
        }

        [Fact]
        public async Task RemoveCheckFromDeposit_CollectedCheck_ShouldFail()
        {
            // Arrange
            var checkIds = await CreateTestChecks(1);
            var deposit = CreateTestDeposit();

            // Deposit and collect the check
            await _checkLifecycleService.DepositChecksAsync(checkIds, deposit);
            var collection = CreateTestCollection(deposit.Id, checkIds[0]);
            await _checkLifecycleService.CollectCheckAsync(checkIds[0], collection);

            // Act - Try to remove collected check
            var result = await _checkLifecycleService.RemoveCheckFromDepositAsync(checkIds[0], deposit.Id, "Test");

            // Assert
            Assert.False(result.IsSuccess);
            Assert.Contains("Only checks in 'Deposited' status can be removed", result.Message);
        }

        [Fact]
        public async Task BulkDeposit_MixedStatuses_ShouldHandleCorrectly()
        {
            // Arrange
            var validCheckIds = await CreateTestChecks(2);
            var invalidCheckIds = await CreateTestChecks(1);
            
            // Deposit one check first to make it invalid for second deposit
            var firstDeposit = CreateTestDeposit();
            await _checkLifecycleService.DepositChecksAsync(new List<Ulid> { invalidCheckIds[0] }, firstDeposit);

            // Try to deposit mix of valid and invalid checks
            var allCheckIds = validCheckIds.Concat(invalidCheckIds).ToList();
            var secondDeposit = CreateTestDeposit();

            // Act
            var result = await _checkLifecycleService.DepositChecksAsync(allCheckIds, secondDeposit);

            // Assert
            Assert.False(result.IsSuccess);
            Assert.Contains("No valid checks found", result.Message);
        }

        [Fact]
        public async Task GetDepositSummary_ShouldReturnCorrectStatistics()
        {
            // Arrange
            var checkIds = await CreateTestChecks(4);
            var deposit = CreateTestDeposit();

            // Deposit all checks
            await _checkLifecycleService.DepositChecksAsync(checkIds, deposit);

            // Collect one check
            var collection = CreateTestCollection(deposit.Id, checkIds[0]);
            await _checkLifecycleService.CollectCheckAsync(checkIds[0], collection);

            // Reject one check
            var rejection = CreateTestRejection(deposit.Id, checkIds[1]);
            await _checkLifecycleService.RejectCheckAsync(checkIds[1], rejection);

            // Act
            var summary = await _context.Set<CheckTreasuryVoucherModel>()
                .Where(c => c.CheckDepositId == deposit.Id)
                .GroupBy(c => c.CheckDepositId)
                .Select(g => new
                {
                    TotalChecks = g.Count(),
                    CollectedChecks = g.Count(c => c.CheckStatusId == CheckStatusEnumeration.Cleared.Value),
                    RejectedChecks = g.Count(c => c.CheckStatusId == CheckStatusEnumeration.Rejected.Value),
                    DepositedChecks = g.Count(c => c.CheckStatusId == CheckStatusEnumeration.Deposited.Value)
                })
                .FirstOrDefaultAsync();

            // Assert
            Assert.NotNull(summary);
            Assert.Equal(4, summary.TotalChecks);
            Assert.Equal(1, summary.CollectedChecks);
            Assert.Equal(1, summary.RejectedChecks);
            Assert.Equal(2, summary.DepositedChecks);
        }

        private async Task<List<Ulid>> CreateTestChecks(int count)
        {
            var checkIds = new List<Ulid>();
            
            for (int i = 0; i < count; i++)
            {
                var check = new CheckTreasuryVoucherModel
                {
                    Id = Ulid.NewUlid(),
                    CheckNumber = $"TEST{i:D3}",
                    DueDate = DateTime.Today.AddDays(30),
                    IssuerName = "Test Issuer",
                    BearerName = "Test Bearer",
                    Amount = 1000m * (i + 1),
                    CheckStatusId = CheckStatusEnumeration.Received.Value,
                    TransactionTypeId = TransactionTypeEnumeration.TreasuryCheckIn.Value,
                    TransactionDate = DateTime.Today,
                    Note = $"Test check {i}",
                    CheckVaultId = Ulid.NewUlid(),
                    CheckVaultLocationId = Ulid.NewUlid()
                };

                _context.Set<CheckTreasuryVoucherModel>().Add(check);
                checkIds.Add(check.Id);
            }

            await _context.SaveChangesAsync();
            return checkIds;
        }

        private CheckDepositModel CreateTestDeposit()
        {
            return new CheckDepositModel
            {
                Id = Ulid.NewUlid(),
                DepositDate = DateTime.Today,
                BankId = Ulid.NewUlid(),
                BankAccountId = Ulid.NewUlid(),
                CheckTreasuryVouchers = new List<CheckTreasuryVoucherModel>(),
                CheckStatusHistories = new List<CheckStatusHistoryModel>()
            };
        }

        private CheckCollectionModel CreateTestCollection(Ulid depositId, Ulid checkId)
        {
            return new CheckCollectionModel
            {
                Id = Ulid.NewUlid(),
                CollectionDate = DateTime.Today.AddDays(1),
                CheckDepositId = depositId,
                CheckTreasuryVoucherId = checkId
            };
        }

        private CheckRejectModel CreateTestRejection(Ulid depositId, Ulid checkId)
        {
            return new CheckRejectModel
            {
                Id = Ulid.NewUlid(),
                RejectDate = DateTime.Today.AddDays(1),
                RefranceNumber = "Insufficient funds",
                CheckDepositId = depositId,
                CheckTreasuryVoucherId = checkId
            };
        }

        private void SeedTestData()
        {
            // Add any required seed data for tests
            // For example, CheckStatus enumerations, TransactionTypes, etc.
            
            _context.SaveChanges();
        }

        public void Dispose()
        {
            _context?.Dispose();
            _serviceProvider?.Dispose();
        }
    }
}
