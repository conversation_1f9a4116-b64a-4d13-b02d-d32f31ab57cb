﻿namespace SimpleBooks.Models.Model.Treasury.BankManagement.CheckManagement
{
    [Table("CheckCollection")]
    [TypeDescriptionProvider(typeof(BaseTypeDescriptionProvider<CheckCollectionModel>))]
    public partial class CheckCollectionModel : BaseModel
    {
        [CustomRequired]
        [DisplayName("Collection Date")]
        [Column(TypeName = "Date"), DataType(DataType.Date)]
        public DateTime CollectionDate { get; set; }

        [CustomRequired]
        [DisplayName("Check Deposit")]
        public Ulid CheckDepositId { get; set; }
        public virtual CheckDepositModel? CheckDeposit { get; set; }

        [CustomRequired]
        [DisplayName("Check Treasury Voucher")]
        public Ulid CheckTreasuryVoucherId { get; set; }
        public virtual CheckTreasuryVoucherModel? CheckTreasuryVoucher { get; set; }

        [CustomRequired]
        [DisplayName("Check Status Histories")]
        public virtual ICollection<CheckStatusHistoryModel> CheckStatusHistories { get; set; } = new List<CheckStatusHistoryModel>();
    }
}
