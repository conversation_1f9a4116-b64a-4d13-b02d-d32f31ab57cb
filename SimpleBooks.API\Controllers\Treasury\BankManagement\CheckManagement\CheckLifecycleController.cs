using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using SimpleBooks.Models.Domain.Treasury.CheckManagement;
using SimpleBooks.Models.Model.Treasury.BankManagement.CheckManagement;
using SimpleBooks.Models.Security;
using SimpleBooks.Services.Server.Business.Treasury.BankManagement.CheckManagement;
using SimpleBooks.Repositories.Core.IRepository.Treasury.BankManagement.CheckManagement;

namespace SimpleBooks.API.Controllers.Treasury.BankManagement.CheckManagement
{
    [Authorize(Policy = CheckManagementPolicies.CanViewChecks)]
    [Route("api/[controller]")]
    [ApiController]
    public class CheckLifecycleController : ControllerBase
    {
        private readonly ICheckLifecycleService _checkLifecycleService;

        public CheckLifecycleController(ICheckLifecycleService checkLifecycleService)
        {
            _checkLifecycleService = checkLifecycleService;
        }

        /// <summary>
        /// Deposit multiple checks to a bank deposit
        /// </summary>
        [HttpPost("deposit")]
        [Authorize(Policy = CheckManagementPolicies.CanDepositChecks)]
        public async Task<IActionResult> DepositChecks([FromBody] DepositChecksRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                    return BadRequest(ModelState);

                var deposit = new CheckDepositModel
                {
                    Id = request.DepositId,
                    DepositDate = request.DepositDate,
                    BankId = request.BankId,
                    BankAccountId = request.BankAccountId,
                    CheckTreasuryVouchers = request.CheckIds.Select(id => new CheckTreasuryVoucherModel { Id = id }).ToList()
                };

                var result = await _checkLifecycleService.DepositChecksAsync(request.CheckIds, deposit);

                if (!result.IsSuccess)
                    return BadRequest(new { Message = result.Message });

                return Ok(new
                {
                    Message = result.Message,
                    DepositId = deposit.Id,
                    ChecksDeposited = result.Data?.Count,
                    Checks = result.Data?.Select(c => new
                    {
                        c.Id,
                        c.CheckNumber,
                        c.Amount,
                        Status = c.Status.Name
                    })
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { Message = $"Internal server error: {ex.Message}" });
            }
        }

        /// <summary>
        /// Remove a check from deposit (rollback operation)
        /// </summary>
        [HttpPost("remove-from-deposit")]
        [Authorize(Policy = CheckManagementPolicies.CanRemoveFromDeposit)]
        public async Task<IActionResult> RemoveCheckFromDeposit([FromBody] RemoveCheckRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                    return BadRequest(ModelState);

                var result = await _checkLifecycleService.RemoveCheckFromDepositAsync(
                    request.CheckId, 
                    request.DepositId, 
                    request.Reason);

                if (!result.IsSuccess)
                    return BadRequest(new { Message = result.Message });

                return Ok(new
                {
                    Message = result.Message,
                    CheckId = result.Data?.Id,
                    CheckNumber = result.Data?.CheckNumber,
                    Status = result.Data?.Status.Name
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { Message = $"Internal server error: {ex.Message}" });
            }
        }

        /// <summary>
        /// Collect a check (mark as cleared)
        /// </summary>
        [HttpPost("collect")]
        public async Task<IActionResult> CollectCheck([FromBody] CollectCheckRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                    return BadRequest(ModelState);

                var collection = new CheckCollectionModel
                {
                    Id = request.CollectionId,
                    CollectionDate = request.CollectionDate,
                    CheckDepositId = request.DepositId,
                    CheckTreasuryVoucherId = request.CheckId
                };

                var result = await _checkLifecycleService.CollectCheckAsync(request.CheckId, collection);

                if (!result.IsSuccess)
                    return BadRequest(new { Message = result.Message });

                return Ok(new
                {
                    Message = result.Message,
                    CheckId = result.Data?.Id,
                    CheckNumber = result.Data?.CheckNumber,
                    Status = result.Data?.Status.Name,
                    CollectionDate = request.CollectionDate
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { Message = $"Internal server error: {ex.Message}" });
            }
        }

        /// <summary>
        /// Reject a check
        /// </summary>
        [HttpPost("reject")]
        public async Task<IActionResult> RejectCheck([FromBody] RejectCheckRequest request)
        {
            try
            {
                if (!ModelState.IsValid)
                    return BadRequest(ModelState);

                var rejection = new CheckRejectModel
                {
                    Id = request.RejectId,
                    RejectDate = request.RejectDate,
                    RefranceNumber = request.Reason,
                    CheckDepositId = request.DepositId,
                    CheckTreasuryVoucherId = request.CheckId
                };

                var result = await _checkLifecycleService.RejectCheckAsync(request.CheckId, rejection);

                if (!result.IsSuccess)
                    return BadRequest(new { Message = result.Message });

                return Ok(new
                {
                    Message = result.Message,
                    CheckId = result.Data?.Id,
                    CheckNumber = result.Data?.CheckNumber,
                    Status = result.Data?.Status.Name,
                    RejectDate = request.RejectDate,
                    Reason = request.Reason
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { Message = $"Internal server error: {ex.Message}" });
            }
        }

        /// <summary>
        /// Get check details with audit trail
        /// </summary>
        [HttpGet("{checkId}")]
        public async Task<IActionResult> GetCheck(string checkId)
        {
            try
            {
                if (!Ulid.TryParse(checkId, out var id))
                    return BadRequest(new { Message = "Invalid check ID format" });

                var result = await _checkLifecycleService.GetCheckAsync(id);

                if (!result.IsSuccess)
                    return NotFound(new { Message = result.Message });

                var check = result.Data;
                return Ok(new
                {
                    check?.Id,
                    check?.CheckNumber,
                    check?.Amount,
                    check?.IssuerName,
                    check?.BearerName,
                    check?.DueDate,
                    Status = check?.Status.Name,
                    check?.DepositId,
                    check?.TransactionDate,
                    check?.Note,
                    CanBeModified = check?.CanBeModified(),
                    IsInDeposit = check?.IsInDeposit()
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { Message = $"Internal server error: {ex.Message}" });
            }
        }

        /// <summary>
        /// Get all checks in a specific deposit
        /// </summary>
        [HttpGet("deposit/{depositId}/checks")]
        public async Task<IActionResult> GetChecksInDeposit(string depositId)
        {
            try
            {
                if (!Ulid.TryParse(depositId, out var id))
                    return BadRequest(new { Message = "Invalid deposit ID format" });

                var result = await _checkLifecycleService.GetChecksInDepositAsync(id);

                if (!result.IsSuccess)
                    return BadRequest(new { Message = result.Message });

                return Ok(new
                {
                    DepositId = id,
                    TotalChecks = result.Data?.Count,
                    Checks = result.Data?.Select(c => new
                    {
                        c.Id,
                        c.CheckNumber,
                        c.Amount,
                        c.IssuerName,
                        Status = c.Status.Name,
                        CanBeRemoved = c.Status.CanBeRemovedFromDeposit(),
                        IsFinalStatus = c.Status.IsFinalStatus()
                    })
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { Message = $"Internal server error: {ex.Message}" });
            }
        }

        /// <summary>
        /// Validate checks for deposit operation
        /// </summary>
        [HttpPost("validate-deposit")]
        public async Task<IActionResult> ValidateDeposit([FromBody] ValidateDepositRequest request)
        {
            try
            {
                var checks = new List<Check>();
                foreach (var checkId in request.CheckIds)
                {
                    var checkResult = await _checkLifecycleService.GetCheckAsync(checkId);
                    if (checkResult.IsSuccess && checkResult.Data != null)
                        checks.Add(checkResult.Data);
                }

                var validation = CheckBusinessRules.ValidateBulkDeposit(checks);

                return Ok(new
                {
                    IsValid = validation.IsValid,
                    Errors = validation.Errors,
                    ValidChecks = checks.Where(c => c.Status == CheckStatus.Received).Count(),
                    TotalChecks = checks.Count
                });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { Message = $"Internal server error: {ex.Message}" });
            }
        }
    }

    // Request DTOs
    public class DepositChecksRequest
    {
        public Ulid DepositId { get; set; } = Ulid.NewUlid();
        public List<Ulid> CheckIds { get; set; } = new();
        public DateTime DepositDate { get; set; } = DateTime.Today;
        public Ulid BankId { get; set; }
        public Ulid BankAccountId { get; set; }
    }

    public class RemoveCheckRequest
    {
        public Ulid CheckId { get; set; }
        public Ulid DepositId { get; set; }
        public string Reason { get; set; } = string.Empty;
    }

    public class CollectCheckRequest
    {
        public Ulid CollectionId { get; set; } = Ulid.NewUlid();
        public Ulid CheckId { get; set; }
        public Ulid DepositId { get; set; }
        public DateTime CollectionDate { get; set; } = DateTime.Today;
    }

    public class RejectCheckRequest
    {
        public Ulid RejectId { get; set; } = Ulid.NewUlid();
        public Ulid CheckId { get; set; }
        public Ulid DepositId { get; set; }
        public DateTime RejectDate { get; set; } = DateTime.Today;
        public string Reason { get; set; } = string.Empty;
    }

    public class ValidateDepositRequest
    {
        public List<Ulid> CheckIds { get; set; } = new();
    }
}
