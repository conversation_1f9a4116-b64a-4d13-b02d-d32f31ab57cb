/**
 * Check Management JavaScript Library
 * Provides client-side functionality for the refactored check management system
 */

class CheckManagement {
    constructor() {
        this.apiBaseUrl = '/api/CheckLifecycle';
        this.webBaseUrl = '/CheckLifecycle';
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.initializeTooltips();
        this.setupAutoRefresh();
    }

    setupEventListeners() {
        // Global error handler for AJAX requests
        $(document).ajaxError((event, xhr, settings, thrownError) => {
            console.error('AJAX Error:', thrownError);
            this.showNotification('An error occurred while processing your request.', 'error');
        });

        // Setup CSRF token for all AJAX requests
        $.ajaxSetup({
            beforeSend: (xhr, settings) => {
                if (!/^(GET|HEAD|OPTIONS|TRACE)$/i.test(settings.type) && !this.csrfSafeMethod(settings.type)) {
                    xhr.setRequestHeader("X-CSRFToken", this.getCsrfToken());
                }
            }
        });
    }

    initializeTooltips() {
        // Initialize Bootstrap tooltips
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(tooltipTriggerEl => new bootstrap.Tooltip(tooltipTriggerEl));
    }

    setupAutoRefresh() {
        // Auto-refresh functionality for real-time updates
        if (window.location.pathname.includes('CheckLifecycle') || 
            window.location.pathname.includes('CheckDeposit/Details')) {
            setInterval(() => {
                this.refreshCheckStatuses();
            }, 30000); // Refresh every 30 seconds
        }
    }

    // Utility Methods
    getCsrfToken() {
        return $('input[name="__RequestVerificationToken"]').val() || 
               $('meta[name="csrf-token"]').attr('content');
    }

    csrfSafeMethod(method) {
        return (/^(GET|HEAD|OPTIONS|TRACE)$/.test(method));
    }

    showNotification(message, type = 'info', duration = 5000) {
        const alertClass = {
            'success': 'alert-success',
            'error': 'alert-danger',
            'warning': 'alert-warning',
            'info': 'alert-info'
        }[type] || 'alert-info';

        const alertHtml = `
            <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
                <i class="bi bi-${this.getIconForType(type)}"></i> ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;

        // Add to notification container or create one
        let container = document.getElementById('notification-container');
        if (!container) {
            container = document.createElement('div');
            container.id = 'notification-container';
            container.className = 'position-fixed top-0 end-0 p-3';
            container.style.zIndex = '9999';
            document.body.appendChild(container);
        }

        const alertElement = document.createElement('div');
        alertElement.innerHTML = alertHtml;
        container.appendChild(alertElement.firstElementChild);

        // Auto-remove after duration
        if (duration > 0) {
            setTimeout(() => {
                const alert = container.querySelector('.alert');
                if (alert) {
                    alert.remove();
                }
            }, duration);
        }
    }

    getIconForType(type) {
        const icons = {
            'success': 'check-circle',
            'error': 'exclamation-triangle',
            'warning': 'exclamation-triangle',
            'info': 'info-circle'
        };
        return icons[type] || 'info-circle';
    }

    // Check Status Management
    async getCheckStatus(checkId) {
        try {
            const response = await fetch(`${this.webBaseUrl}/GetCheckStatus?checkId=${checkId}`);
            const data = await response.json();
            
            if (data.success) {
                return data.data;
            } else {
                throw new Error(data.message);
            }
        } catch (error) {
            console.error('Error getting check status:', error);
            this.showNotification('Error retrieving check status.', 'error');
            return null;
        }
    }

    async validateChecksForDeposit(checkIds) {
        try {
            const response = await fetch(`${this.webBaseUrl}/ValidateChecksForDeposit`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': this.getCsrfToken()
                },
                body: JSON.stringify(checkIds)
            });

            const data = await response.json();
            
            if (data.success) {
                return {
                    isValid: data.isValid,
                    errors: data.errors,
                    validChecks: data.validChecks,
                    totalChecks: data.totalChecks
                };
            } else {
                throw new Error(data.message);
            }
        } catch (error) {
            console.error('Error validating checks:', error);
            this.showNotification('Error validating checks for deposit.', 'error');
            return null;
        }
    }

    async getChecksInDeposit(depositId) {
        try {
            const response = await fetch(`${this.webBaseUrl}/GetChecksInDeposit?depositId=${depositId}`);
            const data = await response.json();
            
            if (data.success) {
                return data.data;
            } else {
                throw new Error(data.message);
            }
        } catch (error) {
            console.error('Error getting checks in deposit:', error);
            this.showNotification('Error retrieving deposit checks.', 'error');
            return null;
        }
    }

    // Real-time Updates
    async refreshCheckStatuses() {
        const checkElements = document.querySelectorAll('[data-check-id]');
        
        for (const element of checkElements) {
            const checkId = element.getAttribute('data-check-id');
            const status = await this.getCheckStatus(checkId);
            
            if (status) {
                this.updateCheckStatusDisplay(element, status);
            }
        }
    }

    updateCheckStatusDisplay(element, status) {
        // Update status badge
        const statusBadge = element.querySelector('.status-badge');
        if (statusBadge) {
            statusBadge.textContent = status.status;
            statusBadge.className = `badge ${this.getStatusBadgeClass(status.status)}`;
        }

        // Update action buttons
        const actionButtons = element.querySelector('.action-buttons');
        if (actionButtons) {
            this.updateActionButtons(actionButtons, status);
        }

        // Update any other status-dependent elements
        element.setAttribute('data-status', status.status);
        element.setAttribute('data-can-modify', status.canBeModified);
        element.setAttribute('data-in-deposit', status.isInDeposit);
    }

    getStatusBadgeClass(status) {
        const classes = {
            'Received': 'badge-info',
            'Deposited': 'badge-warning',
            'Collected': 'badge-success',
            'Rejected': 'badge-danger',
            'Cleared': 'badge-success'
        };
        return classes[status] || 'badge-secondary';
    }

    updateActionButtons(container, status) {
        // Clear existing buttons
        container.innerHTML = '';

        // Add appropriate buttons based on status
        if (status.canBeModified) {
            if (status.status === 'Deposited') {
                container.innerHTML += `
                    <button class="btn btn-sm btn-success" onclick="checkMgmt.collectCheck('${status.id}')">
                        <i class="bi bi-check-circle"></i> Collect
                    </button>
                    <button class="btn btn-sm btn-danger" onclick="checkMgmt.rejectCheck('${status.id}')">
                        <i class="bi bi-x-circle"></i> Reject
                    </button>
                `;
            }
        }

        // Always add details button
        container.innerHTML += `
            <button class="btn btn-sm btn-outline-info" onclick="checkMgmt.showCheckDetails('${status.id}')">
                <i class="bi bi-eye"></i> Details
            </button>
        `;
    }

    // Check Operations
    async collectCheck(checkId) {
        const confirmed = await this.showConfirmDialog(
            'Collect Check',
            'Are you sure you want to collect this check?',
            'success'
        );

        if (confirmed) {
            try {
                // Implementation would depend on the specific form/modal structure
                this.showNotification('Check collection initiated...', 'info');
                // Redirect to collection page or show modal
                window.location.href = `/CheckLifecycle/CollectChecks?checkId=${checkId}`;
            } catch (error) {
                this.showNotification('Error initiating check collection.', 'error');
            }
        }
    }

    async rejectCheck(checkId) {
        const confirmed = await this.showConfirmDialog(
            'Reject Check',
            'Are you sure you want to reject this check? This action cannot be undone.',
            'danger'
        );

        if (confirmed) {
            try {
                // Redirect to rejection page
                window.location.href = `/CheckLifecycle/RejectChecks?checkId=${checkId}`;
            } catch (error) {
                this.showNotification('Error initiating check rejection.', 'error');
            }
        }
    }

    showCheckDetails(checkId) {
        window.location.href = `/CheckLifecycle/CheckDetails/${checkId}`;
    }

    // UI Helpers
    async showConfirmDialog(title, message, type = 'primary') {
        return new Promise((resolve) => {
            const modalHtml = `
                <div class="modal fade" id="confirmModal" tabindex="-1">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">${title}</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <p>${message}</p>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                <button type="button" class="btn btn-${type}" id="confirmBtn">Confirm</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // Remove existing modal if any
            const existingModal = document.getElementById('confirmModal');
            if (existingModal) {
                existingModal.remove();
            }

            // Add new modal
            document.body.insertAdjacentHTML('beforeend', modalHtml);
            const modal = new bootstrap.Modal(document.getElementById('confirmModal'));

            // Setup event listeners
            document.getElementById('confirmBtn').addEventListener('click', () => {
                modal.hide();
                resolve(true);
            });

            document.getElementById('confirmModal').addEventListener('hidden.bs.modal', () => {
                document.getElementById('confirmModal').remove();
                resolve(false);
            });

            modal.show();
        });
    }

    // Form Validation
    validateDepositForm(form) {
        const errors = [];
        
        // Check if at least one check is selected
        const selectedChecks = form.querySelectorAll('input[name="selectedChecks"]:checked');
        if (selectedChecks.length === 0) {
            errors.push('Please select at least one check for deposit.');
        }

        // Validate deposit date
        const depositDate = form.querySelector('input[name="depositDate"]');
        if (depositDate && !depositDate.value) {
            errors.push('Deposit date is required.');
        }

        // Validate bank selection
        const bankId = form.querySelector('select[name="bankId"]');
        if (bankId && !bankId.value) {
            errors.push('Please select a bank.');
        }

        // Validate bank account selection
        const bankAccountId = form.querySelector('select[name="bankAccountId"]');
        if (bankAccountId && !bankAccountId.value) {
            errors.push('Please select a bank account.');
        }

        return {
            isValid: errors.length === 0,
            errors: errors
        };
    }

    // Data Export
    exportToCSV(data, filename) {
        const csv = this.convertToCSV(data);
        const blob = new Blob([csv], { type: 'text/csv' });
        const url = window.URL.createObjectURL(blob);
        
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        a.click();
        
        window.URL.revokeObjectURL(url);
    }

    convertToCSV(data) {
        if (!data || data.length === 0) return '';
        
        const headers = Object.keys(data[0]);
        const csvHeaders = headers.join(',');
        
        const csvRows = data.map(row => 
            headers.map(header => {
                const value = row[header];
                return typeof value === 'string' && value.includes(',') 
                    ? `"${value}"` 
                    : value;
            }).join(',')
        );
        
        return [csvHeaders, ...csvRows].join('\n');
    }
}

// Initialize the check management system
const checkMgmt = new CheckManagement();

// Export for global access
window.CheckManagement = CheckManagement;
window.checkMgmt = checkMgmt;
