﻿namespace SimpleBooks.Repositories.EF.Factory.Config.Treasury.BankManagement.CheckManagement
{
    public class CheckTreasuryVoucherConfiguration : IEntityTypeConfiguration<CheckTreasuryVoucherModel>
    {
        public void Configure(EntityTypeBuilder<CheckTreasuryVoucherModel> builder)
        {
            builder.HasKey(x => new { x.Id });

            builder.HasOne(d => d.BeneficiaryType).WithMany(d => d.CheckTreasuryVouchers)
                .HasForeignKey(d => d.BeneficiaryTypeId)
                .OnDelete(DeleteBehavior.ClientCascade);

            builder.HasOne(d => d.Vendor).WithMany(d => d.CheckTreasuryVouchers)
                .HasForeignKey(d => d.VendorId)
                .OnDelete(DeleteBehavior.ClientCascade)
                .IsRequired(false);

            builder.HasOne(d => d.Customer).WithMany(d => d.CheckTreasuryVouchers)
                .HasForeignKey(d => d.CustomerId)
                .OnDelete(DeleteBehavior.ClientCascade)
                .IsRequired(false);

            builder.HasOne(d => d.Employee).WithMany(d => d.CheckTreasuryVouchers)
                .HasForeignKey(d => d.EmployeeId)
                .OnDelete(DeleteBehavior.ClientCascade)
                .IsRequired(false);

            builder.HasOne(d => d.TransactionType).WithMany(p => p.CheckTreasuryVouchers)
                .HasForeignKey(d => d.TransactionTypeId)
                .OnDelete(DeleteBehavior.ClientCascade);

            builder.HasOne(d => d.CheckStatus).WithMany(p => p.CheckTreasuryVouchers)
                .HasForeignKey(d => d.CheckStatusId)
                .OnDelete(DeleteBehavior.ClientCascade);

            builder.HasOne(d => d.CheckVault).WithMany(p => p.CheckTreasuryVouchers)
                .HasForeignKey(d => d.CheckVaultId)
                .OnDelete(DeleteBehavior.ClientCascade);

            builder.HasOne(d => d.CheckVaultLocation).WithMany(p => p.CheckTreasuryVouchers)
                .HasForeignKey(d => d.CheckVaultLocationId)
                .OnDelete(DeleteBehavior.ClientCascade);

            builder.HasOne(d => d.CheckDeposit).WithMany(p => p.CheckTreasuryVouchers)
                .HasForeignKey(d => d.CheckDepositId)
                .OnDelete(DeleteBehavior.ClientCascade)
                .IsRequired(false);
        }
    }
}
