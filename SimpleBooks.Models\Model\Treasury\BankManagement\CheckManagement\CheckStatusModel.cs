﻿namespace SimpleBooks.Models.Model.Treasury.BankManagement.CheckManagement
{
    [Table("CheckStatus")]
    [TypeDescriptionProvider(typeof(BaseTypeDescriptionProvider<CheckStatusModel>))]
    public partial class CheckStatusModel : BaseModel
    {
        [CustomRequired]
        [DisplayN<PERSON>("Check Status Name")]
        public string CheckStatusName { get; set; }

        [Display<PERSON><PERSON>("Check Status Histories From")]
        public virtual ICollection<CheckStatusHistoryModel> CheckStatusFromHistories { get; set; } = new List<CheckStatusHistoryModel>();
        [DisplayName("Check Status Histories To")]
        public virtual ICollection<CheckStatusHistoryModel> CheckStatusToHistories { get; set; } = new List<CheckStatusHistoryModel>();
        [DisplayName("Check Treasury Vouchers")]
        public virtual ICollection<CheckTreasuryVoucherModel> CheckTreasuryVouchers { get; set; } = new List<CheckTreasuryVoucherModel>();
    }
}
