﻿namespace SimpleBooks.Models.Model.Treasury.BankManagement
{
    [Table("Bank")]
    [TypeDescriptionProvider(typeof(BaseTypeDescriptionProvider<BankModel>))]
    public class BankModel : BaseWithoutTrackingModel
    {
        [CustomRequired]
        [DisplayName("Bank Name")]
        public string BankName { get; set; }
        [DisplayName("Bank Identifier Code")]
        public string? BankIdentifier { get; set; }

        [CustomRequired]
        [DisplayName("Bank Accounts")]
        public virtual ICollection<BankAccountModel> BankAccounts { get; set; } = new List<BankAccountModel>();
        [DisplayName("Bank Transfer Treasury Vouchers")]
        public virtual ICollection<BankTransferTreasuryVoucherModel> BankTransferTreasuryVouchers { get; set; } = new List<BankTransferTreasuryVoucherModel>();
        [DisplayName("Check Deposits")]
        public virtual ICollection<CheckDepositModel> CheckDeposits { get; set; } = new List<CheckDepositModel>();
    }
}
