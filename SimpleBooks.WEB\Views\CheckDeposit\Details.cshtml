@using SimpleBooks.WEB.Controllers.Business.Treasury.BankManagement.CheckManagement
@model DepositDetailsViewModel
@{
    ViewData["Title"] = "Deposit Details";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4 class="card-title mb-0">
                        <i class="bi bi-bank2"></i> Deposit Details
                    </h4>
                    <div>
                        <a href="@Url.Action("Index", "CheckDeposit")" class="btn btn-secondary">
                            <i class="bi bi-arrow-left"></i> Back to Deposits
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <!-- Deposit Information -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <h5>Deposit Information</h5>
                            <table class="table table-borderless">
                                <tr>
                                    <td><strong>Deposit ID:</strong></td>
                                    <td>@Model.Deposit.Id</td>
                                </tr>
                                <tr>
                                    <td><strong>Deposit Date:</strong></td>
                                    <td>@Model.Deposit.DepositDate.ToString("dd/MM/yyyy")</td>
                                </tr>
                                <tr>
                                    <td><strong>Reference Number:</strong></td>
                                    <td>@Model.Deposit.RefranceNumber</td>
                                </tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h5>Summary Statistics</h5>
                            <div class="row">
                                <div class="col-6">
                                    <div class="card bg-primary text-white">
                                        <div class="card-body text-center">
                                            <h3>@Model.Summary.TotalChecks</h3>
                                            <p class="mb-0">Total Checks</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="card bg-success text-white">
                                        <div class="card-body text-center">
                                            <h3>@Model.Summary.TotalAmount.ToString("C")</h3>
                                            <p class="mb-0">Total Amount</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row mt-2">
                                <div class="col-4">
                                    <div class="card bg-info text-white">
                                        <div class="card-body text-center">
                                            <h5>@Model.Summary.DepositedChecks</h5>
                                            <small>Deposited</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-4">
                                    <div class="card bg-success text-white">
                                        <div class="card-body text-center">
                                            <h5>@Model.Summary.CollectedChecks</h5>
                                            <small>Collected</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-4">
                                    <div class="card bg-danger text-white">
                                        <div class="card-body text-center">
                                            <h5>@Model.Summary.RejectedChecks</h5>
                                            <small>Rejected</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Action Buttons -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="btn-group" role="group">
                                <a href="@Url.Action("CollectChecks", "CheckLifecycle", new { depositId = Model.Deposit.Id })" 
                                   class="btn btn-success">
                                    <i class="bi bi-check-circle"></i> Collect Checks
                                </a>
                                <a href="@Url.Action("RejectChecks", "CheckLifecycle", new { depositId = Model.Deposit.Id })" 
                                   class="btn btn-danger">
                                    <i class="bi bi-x-circle"></i> Reject Checks
                                </a>
                                <button type="button" class="btn btn-warning" onclick="refreshSummary()">
                                    <i class="bi bi-arrow-clockwise"></i> Refresh
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Checks in Deposit -->
                    <div class="row">
                        <div class="col-12">
                            <h5>Checks in Deposit</h5>
                            <div class="table-responsive">
                                <table class="table table-striped" id="checksTable">
                                    <thead class="table-dark">
                                        <tr>
                                            <th>Check Number</th>
                                            <th>Issuer</th>
                                            <th>Amount</th>
                                            <th>Status</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        @foreach (var check in Model.Deposit.CheckTreasuryVouchers)
                                        {
                                            <tr>
                                                <td>@check.CheckNumber</td>
                                                <td>@check.IssuerName</td>
                                                <td>@check.Amount.ToString("C")</td>
                                                <td>
                                                    @{
                                                        var statusClass = check.CheckStatusId == CheckStatusEnumeration.Deposited.Value ? "badge-info" :
                                                                         check.CheckStatusId == CheckStatusEnumeration.Cleared.Value ? "badge-success" :
                                                                         check.CheckStatusId == CheckStatusEnumeration.Rejected.Value ? "badge-danger" : "badge-secondary";
                                                        var statusText = check.CheckStatusId == CheckStatusEnumeration.Deposited.Value ? "Deposited" :
                                                                        check.CheckStatusId == CheckStatusEnumeration.Cleared.Value ? "Collected" :
                                                                        check.CheckStatusId == CheckStatusEnumeration.Rejected.Value ? "Rejected" : "Unknown";
                                                    }
                                                    <span class="badge @statusClass">@statusText</span>
                                                </td>
                                                <td>
                                                    @if (Model.RemovableChecks.Any(rc => rc.Id == check.Id))
                                                    {
                                                        <button type="button" class="btn btn-sm btn-outline-warning" 
                                                                onclick="showRemoveModal('@check.Id', '@check.CheckNumber')">
                                                            <i class="bi bi-arrow-left-circle"></i> Remove
                                                        </button>
                                                    }
                                                    <a href="@Url.Action("CheckDetails", "CheckLifecycle", new { checkId = check.Id })" 
                                                       class="btn btn-sm btn-outline-info">
                                                        <i class="bi bi-eye"></i> Details
                                                    </a>
                                                </td>
                                            </tr>
                                        }
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Remove Check Modal -->
<div class="modal fade" id="removeCheckModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Remove Check from Deposit</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="removeCheckForm" method="post" action="@Url.Action("RemoveCheck", "CheckDeposit")">
                <div class="modal-body">
                    <input type="hidden" name="depositId" value="@Model.Deposit.Id" />
                    <input type="hidden" name="checkId" id="removeCheckId" />
                    
                    <p>Are you sure you want to remove check <strong id="removeCheckNumber"></strong> from this deposit?</p>
                    
                    <div class="mb-3">
                        <label for="removeReason" class="form-label">Reason for removal:</label>
                        <textarea class="form-control" id="removeReason" name="reason" rows="3" 
                                  placeholder="Enter reason for removing this check..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-warning">Remove Check</button>
                </div>
            </form>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        function showRemoveModal(checkId, checkNumber) {
            document.getElementById('removeCheckId').value = checkId;
            document.getElementById('removeCheckNumber').textContent = checkNumber;
            new bootstrap.Modal(document.getElementById('removeCheckModal')).show();
        }

        function refreshSummary() {
            // Reload the page to get updated data
            location.reload();
        }

        // Auto-refresh every 30 seconds
        setInterval(function() {
            // You could implement AJAX refresh here instead of full page reload
            // refreshSummary();
        }, 30000);
    </script>
}
