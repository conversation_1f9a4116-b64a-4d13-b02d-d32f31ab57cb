﻿namespace SimpleBooks.Models.ModelMapper.Treasury.BankManagement.CheckManagement
{
    public static class CheckRejectMapper
    {
        public static CreateCheckRejectViewModel ToCreateDto(this CheckRejectModel entity)
        {
            CreateCheckRejectViewModel viewModel = new CreateCheckRejectViewModel()
            {
                RejectDate = entity.RejectDate,
                RefranceNumber = entity.RefranceNumber,
                CheckDepositId = entity.CheckDepositId,
                CheckTreasuryVoucherId = entity.CheckTreasuryVoucherId,
                CheckStatusHistories = entity.CheckStatusHistories.Select(x => x.ToCreateDto()).ToList(),
            };
            return viewModel;
        }

        public static CheckRejectModel ToEntity(this CreateCheckRejectViewModel entity)
        {
            CheckRejectModel model = new CheckRejectModel()
            {
                RejectDate = entity.RejectDate,
                RefranceNumber = entity.RefranceNumber,
                CheckDepositId = entity.CheckDepositId,
                CheckTreasuryVoucherId = entity.CheckTreasuryVoucherId,
                CheckStatusHistories = entity.CheckStatusHistories.Select(x => x.ToEntity()).ToList(),
            };
            return model;
        }

        public static UpdateCheckRejectViewModel ToUpdateDto(this CheckRejectModel entity)
        {
            UpdateCheckRejectViewModel viewModel = new UpdateCheckRejectViewModel()
            {
                Id = entity.Id,
                RejectDate = entity.RejectDate,
                RefranceNumber = entity.RefranceNumber,
                CheckDepositId = entity.CheckDepositId,
                CheckTreasuryVoucherId = entity.CheckTreasuryVoucherId,
                CheckStatusHistories = entity.CheckStatusHistories.Select(x => x.ToUpdateDto()).ToList(),
            };
            return viewModel;
        }

        public static CheckRejectModel ToEntity(this UpdateCheckRejectViewModel entity)
        {
            CheckRejectModel model = new CheckRejectModel()
            {
                Id = entity.Id,
                RejectDate = entity.RejectDate,
                RefranceNumber = entity.RefranceNumber,
                CheckDepositId = entity.CheckDepositId,
                CheckTreasuryVoucherId = entity.CheckTreasuryVoucherId,
                CheckStatusHistories = entity.CheckStatusHistories.Select(x => x.ToEntity()).ToList(),
            };
            return model;
        }
    }
}
