@using SimpleBooks.WEB.Controllers.Business.Treasury.BankManagement.CheckManagement
@model CollectChecksViewModel
@{
    ViewData["Title"] = "Collect Checks";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4 class="card-title mb-0">
                        <i class="bi bi-check-circle"></i> Collect Checks
                    </h4>
                    <div>
                        <a href="@Url.Action("Details", "CheckDeposit", new { id = Model.DepositId })" class="btn btn-secondary">
                            <i class="bi bi-arrow-left"></i> Back to Deposit
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    @if (Model.AvailableChecks.Any())
                    {
                        <div class="alert alert-info">
                            <i class="bi bi-info-circle"></i>
                            Select checks to collect. Only checks in "Deposited" status can be collected.
                        </div>

                        <div class="table-responsive">
                            <table class="table table-striped" id="checksTable">
                                <thead class="table-dark">
                                    <tr>
                                        <th>
                                            <input type="checkbox" id="selectAll" class="form-check-input">
                                        </th>
                                        <th>Check Number</th>
                                        <th>Issuer</th>
                                        <th>Bearer</th>
                                        <th>Amount</th>
                                        <th>Due Date</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var check in Model.AvailableChecks)
                                    {
                                        <tr>
                                            <td>
                                                <input type="checkbox" class="form-check-input check-select" 
                                                       value="@check.Id" data-check-number="@check.CheckNumber">
                                            </td>
                                            <td>@check.CheckNumber</td>
                                            <td>@check.IssuerName</td>
                                            <td>@check.BearerName</td>
                                            <td>@check.Amount.ToString("C")</td>
                                            <td>@check.DueDate.ToString("dd/MM/yyyy")</td>
                                            <td>
                                                <button type="button" class="btn btn-sm btn-success" 
                                                        onclick="collectSingleCheck('@check.Id', '@check.CheckNumber')">
                                                    <i class="bi bi-check-circle"></i> Collect
                                                </button>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>

                        <div class="row mt-3">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label for="collectionDate" class="form-label">Collection Date:</label>
                                    <input type="date" class="form-control" id="collectionDate" 
                                           value="@DateTime.Today.ToString("yyyy-MM-dd")">
                                </div>
                            </div>
                            <div class="col-md-6 d-flex align-items-end">
                                <button type="button" class="btn btn-success me-2" onclick="collectSelectedChecks()">
                                    <i class="bi bi-check-circle-fill"></i> Collect Selected Checks
                                </button>
                                <button type="button" class="btn btn-primary" onclick="collectAllChecks()">
                                    <i class="bi bi-check-all"></i> Collect All Checks
                                </button>
                            </div>
                        </div>
                    }
                    else
                    {
                        <div class="alert alert-warning">
                            <i class="bi bi-exclamation-triangle"></i>
                            No checks available for collection in this deposit.
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Collection Confirmation Modal -->
<div class="modal fade" id="collectionModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Confirm Check Collection</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <p>Are you sure you want to collect the following check(s)?</p>
                <ul id="checksToCollect"></ul>
                <div class="mb-3">
                    <label for="modalCollectionDate" class="form-label">Collection Date:</label>
                    <input type="date" class="form-control" id="modalCollectionDate" 
                           value="@DateTime.Today.ToString("yyyy-MM-dd")">
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-success" onclick="confirmCollection()">
                    <i class="bi bi-check-circle"></i> Confirm Collection
                </button>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        let checksToCollectData = [];

        // Select all functionality
        document.getElementById('selectAll').addEventListener('change', function() {
            const checkboxes = document.querySelectorAll('.check-select');
            checkboxes.forEach(cb => cb.checked = this.checked);
        });

        function collectSingleCheck(checkId, checkNumber) {
            checksToCollectData = [{
                id: checkId,
                number: checkNumber
            }];
            showCollectionModal();
        }

        function collectSelectedChecks() {
            const selectedChecks = document.querySelectorAll('.check-select:checked');
            if (selectedChecks.length === 0) {
                alert('Please select at least one check to collect.');
                return;
            }

            checksToCollectData = Array.from(selectedChecks).map(cb => ({
                id: cb.value,
                number: cb.dataset.checkNumber
            }));

            showCollectionModal();
        }

        function collectAllChecks() {
            const allChecks = document.querySelectorAll('.check-select');
            checksToCollectData = Array.from(allChecks).map(cb => ({
                id: cb.value,
                number: cb.dataset.checkNumber
            }));

            showCollectionModal();
        }

        function showCollectionModal() {
            const checksList = document.getElementById('checksToCollect');
            checksList.innerHTML = '';
            
            checksToCollectData.forEach(check => {
                const li = document.createElement('li');
                li.textContent = `Check ${check.number}`;
                checksList.appendChild(li);
            });

            document.getElementById('modalCollectionDate').value = 
                document.getElementById('collectionDate').value;

            new bootstrap.Modal(document.getElementById('collectionModal')).show();
        }

        function confirmCollection() {
            const collectionDate = document.getElementById('modalCollectionDate').value;
            
            if (!collectionDate) {
                alert('Please select a collection date.');
                return;
            }

            // Show loading state
            const confirmBtn = document.querySelector('#collectionModal .btn-success');
            const originalText = confirmBtn.innerHTML;
            confirmBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> Processing...';
            confirmBtn.disabled = true;

            // Process each check
            let completed = 0;
            const total = checksToCollectData.length;

            checksToCollectData.forEach(check => {
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = '@Url.Action("CollectCheck", "CheckLifecycle")';
                form.style.display = 'none';

                const checkIdInput = document.createElement('input');
                checkIdInput.type = 'hidden';
                checkIdInput.name = 'checkId';
                checkIdInput.value = check.id;

                const depositIdInput = document.createElement('input');
                depositIdInput.type = 'hidden';
                depositIdInput.name = 'depositId';
                depositIdInput.value = '@Model.DepositId';

                const dateInput = document.createElement('input');
                dateInput.type = 'hidden';
                dateInput.name = 'collectionDate';
                dateInput.value = collectionDate;

                form.appendChild(checkIdInput);
                form.appendChild(depositIdInput);
                form.appendChild(dateInput);
                document.body.appendChild(form);

                form.submit();
            });
        }

        // Initialize DataTable if available
        $(document).ready(function() {
            if ($.fn.DataTable) {
                $('#checksTable').DataTable({
                    responsive: true,
                    pageLength: 25,
                    order: [[1, 'asc']] // Sort by check number
                });
            }
        });
    </script>
}
