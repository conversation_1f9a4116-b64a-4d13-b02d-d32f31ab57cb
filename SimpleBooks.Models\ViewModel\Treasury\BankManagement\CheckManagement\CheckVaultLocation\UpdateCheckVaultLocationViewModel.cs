﻿namespace SimpleBooks.Models.ViewModel.Treasury.BankManagement.CheckManagement.CheckVaultLocation
{
    public class UpdateCheckVaultLocationViewModel : BaseUpdateViewModel, IEntityMapper<CheckVaultLocationModel, UpdateCheckVaultLocationViewModel>
    {
        [CustomRequired]
        [DisplayName("Check Vault Account Number")]
        public string CheckVaultLocationNumber { get; set; }
        [CustomRequired]
        [DisplayName("Check Vault Account Currency")]
        public string CheckVaultLocationCurrency { get; set; }

        [DisplayName("CheckVault")]
        public Ulid CheckVaultId { get; set; }

        public UpdateCheckVaultLocationViewModel ToDto(CheckVaultLocationModel entity) => entity.ToUpdateDto();

        public CheckVaultLocationModel ToEntity() => CheckVaultLocationMapper.ToEntity(this);
    }
}
