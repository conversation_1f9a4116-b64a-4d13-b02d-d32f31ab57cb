﻿namespace SimpleBooks.Models.Model.Treasury.CashManagement
{
    [Table("Drawer")]
    [TypeDescriptionProvider(typeof(BaseTypeDescriptionProvider<DrawerModel>))]
    public class DrawerModel : BaseWithoutTrackingModel
    {
        [CustomRequired]
        [DisplayName("Drawer Name")]
        public string DrawerName { get; set; }

        [CustomRequired]
        [DisplayName("Drawer Locations")]
        public virtual ICollection<DrawerLocationModel> DrawerLocations { get; set; } = new List<DrawerLocationModel>();
        [DisplayName("Cash Treasury Vouchers")]
        public virtual ICollection<CashTreasuryVoucherModel> CashTreasuryVouchers { get; set; } = new List<CashTreasuryVoucherModel>();
    }
}
