using SimpleBooks.Models.Domain.Treasury.CheckManagement;
using SimpleBooks.Models.Enumerations;
using Xunit;

namespace SimpleBooks.Tests.Domain.Treasury.CheckManagement
{
    public class CheckStatusTests
    {
        [Fact]
        public void CheckStatus_FromValue_ShouldReturnCorrectStatus()
        {
            // Arrange & Act
            var received = CheckStatus.FromValue(CheckStatusEnumeration.Received.Value);
            var deposited = CheckStatus.FromValue(CheckStatusEnumeration.Deposited.Value);
            var cleared = CheckStatus.FromValue(CheckStatusEnumeration.Cleared.Value);

            // Assert
            Assert.Equal(CheckStatus.Received, received);
            Assert.Equal(CheckStatus.Deposited, deposited);
            Assert.Equal(CheckStatus.Cleared, cleared);
        }

        [Fact]
        public void CheckStatus_FromValue_InvalidValue_ShouldThrowException()
        {
            // Arrange
            var invalidValue = Ulid.NewUlid();

            // Act & Assert
            Assert.Throws<ArgumentException>(() => CheckStatus.FromValue(invalidValue));
        }

        [Fact]
        public void CheckStatus_IsInitialStatus_ShouldReturnCorrectValues()
        {
            // Assert
            Assert.True(CheckStatus.Received.IsInitialStatus());
            Assert.True(CheckStatus.Issued.IsInitialStatus());
            Assert.True(CheckStatus.Unknown.IsInitialStatus());
            Assert.False(CheckStatus.Deposited.IsInitialStatus());
            Assert.False(CheckStatus.Cleared.IsInitialStatus());
        }

        [Fact]
        public void CheckStatus_IsFinalStatus_ShouldReturnCorrectValues()
        {
            // Assert
            Assert.True(CheckStatus.Cleared.IsFinalStatus());
            Assert.True(CheckStatus.Rejected.IsFinalStatus());
            Assert.True(CheckStatus.Returned.IsFinalStatus());
            Assert.False(CheckStatus.Received.IsFinalStatus());
            Assert.False(CheckStatus.Deposited.IsFinalStatus());
        }

        [Fact]
        public void CheckStatus_CanBeRemovedFromDeposit_ShouldReturnCorrectValues()
        {
            // Assert
            Assert.True(CheckStatus.Deposited.CanBeRemovedFromDeposit());
            Assert.False(CheckStatus.Received.CanBeRemovedFromDeposit());
            Assert.False(CheckStatus.Cleared.CanBeRemovedFromDeposit());
            Assert.False(CheckStatus.Rejected.CanBeRemovedFromDeposit());
        }

        [Theory]
        [InlineData(true)] // TreasuryCheckIn
        [InlineData(false)] // TreasuryCheckOut
        public void CheckStatus_CanTransitionTo_ValidTransitions_ShouldReturnTrue(bool isCheckIn)
        {
            // Arrange
            var transactionType = isCheckIn 
                ? TransactionTypeEnumeration.TreasuryCheckIn.Value 
                : TransactionTypeEnumeration.TreasuryCheckOut.Value;

            if (isCheckIn)
            {
                // Assert valid transitions for received checks
                Assert.True(CheckStatus.Received.CanTransitionTo(CheckStatus.Deposited, transactionType));
                Assert.True(CheckStatus.Deposited.CanTransitionTo(CheckStatus.Cleared, transactionType));
                Assert.True(CheckStatus.Deposited.CanTransitionTo(CheckStatus.Rejected, transactionType));
                Assert.True(CheckStatus.Rejected.CanTransitionTo(CheckStatus.Returned, transactionType));
            }
            else
            {
                // Assert valid transitions for issued checks
                Assert.True(CheckStatus.Issued.CanTransitionTo(CheckStatus.Cleared, transactionType));
                Assert.True(CheckStatus.Issued.CanTransitionTo(CheckStatus.Returned, transactionType));
            }
        }

        [Theory]
        [InlineData(true)] // TreasuryCheckIn
        [InlineData(false)] // TreasuryCheckOut
        public void CheckStatus_CanTransitionTo_InvalidTransitions_ShouldReturnFalse(bool isCheckIn)
        {
            // Arrange
            var transactionType = isCheckIn 
                ? TransactionTypeEnumeration.TreasuryCheckIn.Value 
                : TransactionTypeEnumeration.TreasuryCheckOut.Value;

            if (isCheckIn)
            {
                // Assert invalid transitions for received checks
                Assert.False(CheckStatus.Received.CanTransitionTo(CheckStatus.Cleared, transactionType));
                Assert.False(CheckStatus.Received.CanTransitionTo(CheckStatus.Rejected, transactionType));
                Assert.False(CheckStatus.Cleared.CanTransitionTo(CheckStatus.Deposited, transactionType));
                Assert.False(CheckStatus.Rejected.CanTransitionTo(CheckStatus.Deposited, transactionType));
            }
            else
            {
                // Assert invalid transitions for issued checks
                Assert.False(CheckStatus.Issued.CanTransitionTo(CheckStatus.Deposited, transactionType));
                Assert.False(CheckStatus.Issued.CanTransitionTo(CheckStatus.Rejected, transactionType));
                Assert.False(CheckStatus.Cleared.CanTransitionTo(CheckStatus.Issued, transactionType));
            }
        }

        [Fact]
        public void CheckStatus_Equality_ShouldWorkCorrectly()
        {
            // Arrange
            var status1 = CheckStatus.Received;
            var status2 = CheckStatus.FromValue(CheckStatusEnumeration.Received.Value);
            var status3 = CheckStatus.Deposited;

            // Assert
            Assert.Equal(status1, status2);
            Assert.True(status1 == status2);
            Assert.False(status1 == status3);
            Assert.True(status1 != status3);
            Assert.False(status1.Equals(null));
        }

        [Fact]
        public void CheckStatus_ToString_ShouldReturnName()
        {
            // Assert
            Assert.Equal("Received", CheckStatus.Received.ToString());
            Assert.Equal("Deposited", CheckStatus.Deposited.ToString());
            Assert.Equal("Cleared", CheckStatus.Cleared.ToString());
        }
    }
}
