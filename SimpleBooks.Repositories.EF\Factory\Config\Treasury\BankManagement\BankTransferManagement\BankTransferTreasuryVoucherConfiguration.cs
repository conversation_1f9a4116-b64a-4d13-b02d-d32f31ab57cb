﻿namespace SimpleBooks.Repositories.EF.Factory.Config.Treasury.BankManagement.BankTransferManagement
{
    public class BankTransferTreasuryVoucherConfiguration : IEntityTypeConfiguration<BankTransferTreasuryVoucherModel>
    {
        public void Configure(EntityTypeBuilder<BankTransferTreasuryVoucherModel> builder)
        {
            builder.HasKey(x => new { x.Id });

            builder.HasOne(d => d.BeneficiaryType).WithMany(d => d.BankTransferTreasuryVouchers)
                .HasForeignKey(d => d.BeneficiaryTypeId)
                .OnDelete(DeleteBehavior.ClientCascade);

            builder.HasOne(d => d.Vendor).WithMany(d => d.BankTransferTreasuryVouchers)
                .HasForeignKey(d => d.VendorId)
                .OnDelete(DeleteBehavior.ClientCascade)
                .IsRequired(false);

            builder.HasOne(d => d.Customer).WithMany(d => d.BankTransferTreasuryVouchers)
                .HasForeignKey(d => d.CustomerId)
                .OnDelete(DeleteBehavior.ClientCascade)
                .IsRequired(false);

            builder.HasOne(d => d.Employee).WithMany(d => d.BankTransferTreasuryVouchers)
                .HasForeignKey(d => d.EmployeeId)
                .OnDelete(DeleteBehavior.ClientCascade)
                .IsRequired(false);

            builder.HasOne(d => d.TransactionType).WithMany(p => p.BankTransferTreasuryVouchers)
                .HasForeignKey(d => d.TransactionTypeId)
                .OnDelete(DeleteBehavior.ClientCascade);

            builder.HasOne(d => d.Bank).WithMany(p => p.BankTransferTreasuryVouchers)
                .HasForeignKey(d => d.BankId)
                .OnDelete(DeleteBehavior.ClientCascade);

            builder.HasOne(d => d.BankAccount).WithMany(p => p.BankTransferTreasuryVouchers)
                .HasForeignKey(d => d.BankAccountId)
                .OnDelete(DeleteBehavior.ClientCascade);
        }
    }
}
