﻿namespace SimpleBooks.Models.Model.Treasury.BankManagement
{
    [Table("BankAccount")]
    [TypeDescriptionProvider(typeof(BaseTypeDescriptionProvider<BankAccountModel>))]
    public class BankAccountModel : BaseWithoutTrackingModel
    {
        [CustomRequired]
        [DisplayName("Bank Account Number")]
        public string BankAccountNumber { get; set; }
        [CustomRequired]
        [DisplayName("Bank Account Currency")]
        public string BankAccountCurrency { get; set; }
        [DisplayName("Bank Account IBAN")]
        public string? BankAccountIBAN { get; set; }
        [DisplayName("Bank Account Swift Code")]
        public string? BankAccountSwiftCode { get; set; }

        [CustomRequired]
        [DisplayName("Bank")]
        public Ulid BankId { get; set; }
        public virtual BankModel? Bank { get; set; }

        [DisplayName("Bank Transfer Treasury Vouchers")]
        public virtual ICollection<BankTransferTreasuryVoucherModel> BankTransferTreasuryVouchers { get; set; } = new List<BankTransferTreasuryVoucherModel>();
        [DisplayName("Check Deposits")]
        public virtual ICollection<CheckDepositModel> CheckDeposits { get; set; } = new List<CheckDepositModel>();
    }
}
