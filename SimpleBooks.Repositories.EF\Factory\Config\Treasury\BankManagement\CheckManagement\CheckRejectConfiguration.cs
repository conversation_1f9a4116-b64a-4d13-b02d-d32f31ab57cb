﻿namespace SimpleBooks.Repositories.EF.Factory.Config.Treasury.BankManagement.BankTransferManagement
{
    public class CheckRejectConfiguration : IEntityTypeConfiguration<CheckRejectModel>
    {
        public void Configure(EntityTypeBuilder<CheckRejectModel> builder)
        {
            builder.<PERSON>Key(x => new { x.Id });

            builder.HasOne(d => d.CheckDeposit).WithMany(p => p.CheckRejects)
                .HasForeignKey(d => d.CheckDepositId)
                .OnDelete(DeleteBehavior.ClientCascade);

            builder.HasOne(d => d.CheckTreasuryVoucher).WithMany(p => p.CheckRejects)
                .HasForeignKey(d => d.CheckTreasuryVoucherId)
                .OnDelete(DeleteBehavior.ClientCascade);
        }
    }
}
