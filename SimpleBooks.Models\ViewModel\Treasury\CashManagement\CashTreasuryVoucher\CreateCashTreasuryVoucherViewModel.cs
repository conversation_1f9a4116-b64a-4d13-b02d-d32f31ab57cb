﻿namespace SimpleBooks.Models.ViewModel.Treasury.CashManagement.CashTreasuryVoucher
{
    public class CreateCashTreasuryVoucherViewModel : CreateTreasuryVoucherViewModel, IEntityMapper<CashTreasuryVoucherModel, CreateCashTreasuryVoucherViewModel>
    {
        [CustomRequired]
        [DisplayName("Drawer")]
        public Ulid DrawerId { get; set; }

        [CustomRequired]
        [DisplayName("Drawer Location")]
        public Ulid DrawerLocationId { get; set; }

        public CreateCashTreasuryVoucherViewModel ToDto(CashTreasuryVoucherModel entity) => entity.ToCreateDto();

        public CashTreasuryVoucherModel ToEntity() => CashTreasuryVoucherMapper.ToEntity(this);
    }
}
