﻿namespace SimpleBooks.Models.Model.Treasury.CashManagement
{
    [Table("DrawerLocation")]
    [TypeDescriptionProvider(typeof(BaseTypeDescriptionProvider<DrawerLocationModel>))]
    public class DrawerLocationModel : BaseWithoutTrackingModel
    {
        [CustomRequired]
        [DisplayName("Drawer Account Number")]
        public string DrawerLocationNumber { get; set; }
        [CustomRequired]
        [DisplayName("Drawer Account Currency")]
        public string DrawerLocationCurrency { get; set; }

        [CustomRequired]
        [DisplayName("Drawer")]
        public Ulid DrawerId { get; set; }
        public virtual DrawerModel? Drawer { get; set; }

        [DisplayName("Cash Treasury Vouchers")]
        public virtual ICollection<CashTreasuryVoucherModel> CashTreasuryVouchers { get; set; } = new List<CashTreasuryVoucherModel>();
    }
}
