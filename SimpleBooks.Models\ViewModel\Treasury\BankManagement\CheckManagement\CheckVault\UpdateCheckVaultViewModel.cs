﻿namespace SimpleBooks.Models.ViewModel.Treasury.BankManagement.CheckManagement.CheckVault
{
    public class UpdateCheckVaultViewModel : BaseUpdateViewModel, IEntityMapper<CheckVaultModel, UpdateCheckVaultViewModel>
    {
        [CustomRequired]
        [DisplayName("Check Vault Name")]
        public string CheckVaultName { get; set; }

        [CustomRequired]
        [DisplayName("CheckVault Locations")]
        public IList<UpdateCheckVaultLocationViewModel> CheckVaultLocations { get; set; } = new List<UpdateCheckVaultLocationViewModel>();

        public UpdateCheckVaultViewModel ToDto(CheckVaultModel entity) => entity.ToUpdateDto();

        public CheckVaultModel ToEntity() => CheckVaultMapper.ToEntity(this);
    }
}
