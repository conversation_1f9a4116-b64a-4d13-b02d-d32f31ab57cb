﻿namespace SimpleBooks.Models.ModelMapper.Treasury.BankManagement.BankTransferManagement
{
    public static class BankTransferTreasuryVoucherMapper
    {
        public static CreateBankTransferTreasuryVoucherViewModel ToCreateDto(this BankTransferTreasuryVoucherModel entity)
        {
            CreateBankTransferTreasuryVoucherViewModel viewModel = new CreateBankTransferTreasuryVoucherViewModel()
            {
                TransactionDate = entity.TransactionDate,
                Amount = entity.Amount,
                Note = entity.Note,
                RefranceNumber = entity.RefranceNumber,
                TransactionTypeId = entity.TransactionTypeId,
                BeneficiaryTypeId = entity.BeneficiaryTypeId,
                BeneficiaryId = entity.GetBeneficiaryId(),
                BankId = entity.BankId,
                BankAccountId = entity.BankAccountId,
                TreasuryLines = entity.TreasuryLines.Select(x => x.ToCreateDto()).ToList(),
            };
            return viewModel;
        }

        public static BankTransferTreasuryVoucherModel ToEntity(this CreateBankTransferTreasuryVoucherViewModel entity)
        {
            BankTransferTreasuryVoucherModel model = new BankTransferTreasuryVoucherModel()
            {
                TransactionDate = entity.TransactionDate,
                Amount = entity.Amount,
                Note = entity.Note,
                RefranceNumber = entity.RefranceNumber,
                TransactionTypeId = entity.TransactionTypeId,
                BeneficiaryTypeId = entity.BeneficiaryTypeId,
                VendorId = entity.BeneficiaryTypeId == BeneficiaryTypeEnumeration.Vendor.Value ? entity.BeneficiaryId : null,
                CustomerId = entity.BeneficiaryTypeId == BeneficiaryTypeEnumeration.Customer.Value ? entity.BeneficiaryId : null,
                EmployeeId = entity.BeneficiaryTypeId == BeneficiaryTypeEnumeration.Employee.Value ? entity.BeneficiaryId : null,
                BankId = entity.BankId,
                BankAccountId = entity.BankAccountId,
                TreasuryLines = entity.TreasuryLines.Select(x => x.ToEntity()).ToList(),
            };
            return model;
        }

        public static UpdateBankTransferTreasuryVoucherViewModel ToUpdateDto(this BankTransferTreasuryVoucherModel entity)
        {
            UpdateBankTransferTreasuryVoucherViewModel viewModel = new UpdateBankTransferTreasuryVoucherViewModel()
            {
                Id = entity.Id,
                TransactionDate = entity.TransactionDate,
                VoucherSerial = entity.VoucherSerial,
                TVID = entity.TVID,
                Amount = entity.Amount,
                Note = entity.Note,
                RefranceNumber = entity.RefranceNumber,
                TransactionTypeId = entity.TransactionTypeId,
                BeneficiaryTypeId = entity.BeneficiaryTypeId,
                BeneficiaryId = entity.GetBeneficiaryId(),
                BankId = entity.BankId,
                BankAccountId = entity.BankAccountId,
                TreasuryLines = entity.TreasuryLines.Select(x => x.ToUpdateDto()).ToList(),
            };
            return viewModel;
        }

        public static BankTransferTreasuryVoucherModel ToEntity(this UpdateBankTransferTreasuryVoucherViewModel entity)
        {
            BankTransferTreasuryVoucherModel model = new BankTransferTreasuryVoucherModel()
            {
                Id = entity.Id,
                TransactionDate = entity.TransactionDate,
                VoucherSerial = entity.VoucherSerial,
                TVID = entity.TVID,
                Amount = entity.Amount,
                Note = entity.Note,
                RefranceNumber = entity.RefranceNumber,
                TransactionTypeId = entity.TransactionTypeId,
                BeneficiaryTypeId = entity.BeneficiaryTypeId,
                VendorId = entity.BeneficiaryTypeId == BeneficiaryTypeEnumeration.Vendor.Value ? entity.BeneficiaryId : null,
                CustomerId = entity.BeneficiaryTypeId == BeneficiaryTypeEnumeration.Customer.Value ? entity.BeneficiaryId : null,
                EmployeeId = entity.BeneficiaryTypeId == BeneficiaryTypeEnumeration.Employee.Value ? entity.BeneficiaryId : null,
                BankId = entity.BankId,
                BankAccountId = entity.BankAccountId,
                TreasuryLines = entity.TreasuryLines.Select(x => x.ToEntity()).ToList(),
            };
            return model;
        }

        public static Ulid GetBeneficiaryId(this BankTransferTreasuryVoucherModel entity)
        {
            if (entity.VendorId.HasValue)
                return entity.VendorId.Value;
            else if (entity.CustomerId.HasValue)
                return entity.CustomerId.Value;
            else if (entity.EmployeeId.HasValue)
                return entity.EmployeeId.Value;
            throw new InvalidOperationException("No beneficiary ID found.");
        }
    }
}
