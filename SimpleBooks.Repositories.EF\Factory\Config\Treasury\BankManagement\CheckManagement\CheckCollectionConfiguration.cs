﻿namespace SimpleBooks.Repositories.EF.Factory.Config.Treasury.BankManagement.BankTransferManagement
{
    public class CheckCollectionConfiguration : IEntityTypeConfiguration<CheckCollectionModel>
    {
        public void Configure(EntityTypeBuilder<CheckCollectionModel> builder)
        {
            builder.<PERSON><PERSON>ey(x => new { x.Id });

            builder.<PERSON>One(d => d.CheckDeposit).WithMany(p => p.CheckCollections)
                .HasForeignKey(d => d.CheckDepositId)
                .OnDelete(DeleteBehavior.ClientCascade);

            builder.HasOne(d => d.CheckTreasuryVoucher).WithMany(p => p.CheckCollections)
                .HasForeignKey(d => d.CheckTreasuryVoucherId)
                .OnDelete(DeleteBehavior.ClientCascade);
        }
    }
}
