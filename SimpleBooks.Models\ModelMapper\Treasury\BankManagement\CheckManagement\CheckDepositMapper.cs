﻿namespace SimpleBooks.Models.ModelMapper.Treasury.BankManagement.CheckManagement
{
    public static class CheckDepositMapper
    {
        public static CreateCheckDepositViewModel ToCreateDto(this CheckDepositModel entity)
        {
            CreateCheckDepositViewModel viewModel = new CreateCheckDepositViewModel()
            {
                DepositDate = entity.DepositDate,
                RefranceNumber = entity.RefranceNumber,
                BankId = entity.BankId,
                BankAccountId = entity.BankAccountId,
                SelectedCheckTreasuryVouchers = entity.CheckTreasuryVouchers.Select(x => x.Id).ToList(),
            };
            return viewModel;
        }

        public static CheckDepositModel ToEntity(this CreateCheckDepositViewModel entity)
        {
            CheckDepositModel model = new CheckDepositModel()
            {
                DepositDate = entity.DepositDate,
                RefranceNumber = entity.RefranceNumber,
                BankId = entity.BankId,
                BankAccountId = entity.BankAccountId,
                CheckTreasuryVouchers = entity.SelectedCheckTreasuryVouchers.Select(x => new CheckTreasuryVoucherModel()
                {
                    Id = x,
                }).ToList(),
            };
            return model;
        }

        public static UpdateCheckDepositViewModel ToUpdateDto(this CheckDepositModel entity)
        {
            UpdateCheckDepositViewModel viewModel = new UpdateCheckDepositViewModel()
            {
                Id = entity.Id,
                DepositDate = entity.DepositDate,
                RefranceNumber = entity.RefranceNumber,
                BankId = entity.BankId,
                BankAccountId = entity.BankAccountId,
                SelectedCheckTreasuryVouchers = entity.CheckTreasuryVouchers.Select(x => x.Id).ToList(),
            };
            return viewModel;
        }

        public static CheckDepositModel ToEntity(this UpdateCheckDepositViewModel entity)
        {
            CheckDepositModel model = new CheckDepositModel()
            {
                Id = entity.Id,
                DepositDate = entity.DepositDate,
                RefranceNumber = entity.RefranceNumber,
                BankId = entity.BankId,
                BankAccountId = entity.BankAccountId,
                CheckTreasuryVouchers = entity.SelectedCheckTreasuryVouchers.Select(x => new CheckTreasuryVoucherModel()
                {
                    Id = x,
                }).ToList(),
            };
            return model;
        }
    }
}
