﻿namespace SimpleBooks.API.Controllers.Business.Treasury.BankManagement.CheckManagement
{
    public class CheckStatusHistoryController : BaseBusinessController<CheckStatusHistoryModel, CheckStatusHistoryModel, C<PERSON>CheckStatusHistoryViewModel, UpdateCheckStatusHistoryViewModel>
    {
        private readonly ICheckStatusHistoryService _CheckStatusHistoryService;

        public CheckStatusHistoryController(ICheckStatusHistoryService CheckStatusHistoryService) : base(CheckStatusHistoryService)
        {
            _CheckStatusHistoryService = CheckStatusHistoryService;
        }
    }
}
