@using SimpleBooks.WEB.Controllers.Business.Treasury.BankManagement.CheckManagement
@model CheckManagementDashboardViewModel
@{
    ViewData["Title"] = "Check Management Dashboard";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<div class="container-fluid">
    <!-- Dashboard Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <h2><i class="bi bi-speedometer2"></i> Check Management Dashboard</h2>
                <div class="btn-group">
                    <a href="@Url.Action("Index", "CheckTreasuryVoucher")" class="btn btn-primary">
                        <i class="bi bi-plus-circle"></i> New Check
                    </a>
                    <a href="@Url.Action("Index", "CheckDeposit")" class="btn btn-success">
                        <i class="bi bi-bank"></i> Deposits
                    </a>
                    <button type="button" class="btn btn-info" onclick="refreshDashboard()">
                        <i class="bi bi-arrow-clockwise"></i> Refresh
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-2 col-md-4 col-sm-6 mb-3">
            <div class="card bg-primary text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h3 class="mb-0" id="totalChecks">@Model.TotalChecks</h3>
                            <p class="mb-0">Total Checks</p>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-file-earmark-check fs-1"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-xl-2 col-md-4 col-sm-6 mb-3">
            <div class="card bg-info text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h3 class="mb-0" id="receivedChecks">@Model.ReceivedChecks</h3>
                            <p class="mb-0">Received</p>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-inbox fs-1"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-2 col-md-4 col-sm-6 mb-3">
            <div class="card bg-warning text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h3 class="mb-0" id="depositedChecks">@Model.DepositedChecks</h3>
                            <p class="mb-0">Deposited</p>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-bank2 fs-1"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-2 col-md-4 col-sm-6 mb-3">
            <div class="card bg-success text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h3 class="mb-0" id="collectedChecks">@Model.CollectedChecks</h3>
                            <p class="mb-0">Collected</p>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-check-circle fs-1"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-2 col-md-4 col-sm-6 mb-3">
            <div class="card bg-danger text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h3 class="mb-0" id="rejectedChecks">@Model.RejectedChecks</h3>
                            <p class="mb-0">Rejected</p>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-x-circle fs-1"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-2 col-md-4 col-sm-6 mb-3">
            <div class="card bg-dark text-white h-100">
                <div class="card-body">
                    <div class="d-flex justify-content-between">
                        <div>
                            <h3 class="mb-0" id="totalAmount">@Model.TotalAmount.ToString("C")</h3>
                            <p class="mb-0">Total Value</p>
                        </div>
                        <div class="align-self-center">
                            <i class="bi bi-currency-dollar fs-1"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-lightning"></i> Quick Actions
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <div class="d-grid">
                                <button type="button" class="btn btn-outline-primary btn-lg" onclick="showCheckValidator()">
                                    <i class="bi bi-shield-check"></i><br>
                                    Validate Checks
                                </button>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="d-grid">
                                <a href="@Url.Action("Create", "CheckDeposit")" class="btn btn-outline-success btn-lg">
                                    <i class="bi bi-plus-square"></i><br>
                                    Create Deposit
                                </a>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="d-grid">
                                <button type="button" class="btn btn-outline-info btn-lg" onclick="showStatusTracker()">
                                    <i class="bi bi-search"></i><br>
                                    Track Check
                                </button>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="d-grid">
                                <button type="button" class="btn btn-outline-warning btn-lg" onclick="showReports()">
                                    <i class="bi bi-graph-up"></i><br>
                                    Reports
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activity -->
    <div class="row">
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-clock-history"></i> Recent Deposits
                    </h5>
                </div>
                <div class="card-body">
                    <div id="recentDeposits">
                        <div class="text-center">
                            <div class="spinner-border" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-exclamation-triangle"></i> Pending Actions
                    </h5>
                </div>
                <div class="card-body">
                    <div id="pendingActions">
                        <div class="text-center">
                            <div class="spinner-border" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Check Validator Modal -->
<div class="modal fade" id="checkValidatorModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-shield-check"></i> Check Validator
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="checkIdsInput" class="form-label">Enter Check IDs (one per line):</label>
                    <textarea class="form-control" id="checkIdsInput" rows="5" 
                              placeholder="Enter check IDs to validate for deposit..."></textarea>
                </div>
                <div id="validationResults"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" onclick="validateChecks()">
                    <i class="bi bi-check-circle"></i> Validate
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Status Tracker Modal -->
<div class="modal fade" id="statusTrackerModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-search"></i> Check Status Tracker
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <label for="trackCheckId" class="form-label">Check ID:</label>
                    <input type="text" class="form-control" id="trackCheckId" 
                           placeholder="Enter check ID to track...">
                </div>
                <div id="trackingResults"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-info" onclick="trackCheck()">
                    <i class="bi bi-search"></i> Track
                </button>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        function refreshDashboard() {
            location.reload();
        }

        function showCheckValidator() {
            new bootstrap.Modal(document.getElementById('checkValidatorModal')).show();
        }

        function showStatusTracker() {
            new bootstrap.Modal(document.getElementById('statusTrackerModal')).show();
        }

        function showReports() {
            // Navigate to reports page
            window.location.href = '@Url.Action("Index", "Reports")';
        }

        function validateChecks() {
            const input = document.getElementById('checkIdsInput').value.trim();
            if (!input) {
                alert('Please enter check IDs to validate.');
                return;
            }

            const checkIds = input.split('\n').map(id => id.trim()).filter(id => id);
            
            fetch('@Url.Action("ValidateChecksForDeposit", "CheckLifecycle")', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'RequestVerificationToken': $('input[name="__RequestVerificationToken"]').val()
                },
                body: JSON.stringify(checkIds)
            })
            .then(response => response.json())
            .then(data => {
                const resultsDiv = document.getElementById('validationResults');
                if (data.success) {
                    resultsDiv.innerHTML = `
                        <div class="alert ${data.isValid ? 'alert-success' : 'alert-warning'}">
                            <h6>Validation Results:</h6>
                            <p>Valid Checks: ${data.validChecks} / ${data.totalChecks}</p>
                            ${data.errors.length > 0 ? '<ul>' + data.errors.map(e => '<li>' + e + '</li>').join('') + '</ul>' : ''}
                        </div>
                    `;
                } else {
                    resultsDiv.innerHTML = `<div class="alert alert-danger">${data.message}</div>`;
                }
            })
            .catch(error => {
                console.error('Error:', error);
                document.getElementById('validationResults').innerHTML = 
                    '<div class="alert alert-danger">Error validating checks.</div>';
            });
        }

        function trackCheck() {
            const checkId = document.getElementById('trackCheckId').value.trim();
            if (!checkId) {
                alert('Please enter a check ID to track.');
                return;
            }

            fetch(`@Url.Action("GetCheckStatus", "CheckLifecycle")?checkId=${checkId}`)
            .then(response => response.json())
            .then(data => {
                const resultsDiv = document.getElementById('trackingResults');
                if (data.success) {
                    resultsDiv.innerHTML = `
                        <div class="alert alert-info">
                            <h6>Check Information:</h6>
                            <p><strong>Check Number:</strong> ${data.data.checkNumber}</p>
                            <p><strong>Status:</strong> <span class="badge bg-primary">${data.data.status}</span></p>
                            <p><strong>Amount:</strong> ${data.data.amount}</p>
                            <p><strong>Can be Modified:</strong> ${data.data.canBeModified ? 'Yes' : 'No'}</p>
                            <p><strong>In Deposit:</strong> ${data.data.isInDeposit ? 'Yes' : 'No'}</p>
                        </div>
                    `;
                } else {
                    resultsDiv.innerHTML = `<div class="alert alert-danger">${data.message}</div>`;
                }
            })
            .catch(error => {
                console.error('Error:', error);
                document.getElementById('trackingResults').innerHTML = 
                    '<div class="alert alert-danger">Error tracking check.</div>';
            });
        }

        // Load recent activity on page load
        $(document).ready(function() {
            // Load recent deposits
            setTimeout(() => {
                document.getElementById('recentDeposits').innerHTML = `
                    <div class="list-group">
                        <div class="list-group-item">
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1">Deposit #12345</h6>
                                <small>2 hours ago</small>
                            </div>
                            <p class="mb-1">5 checks deposited - $15,000.00</p>
                        </div>
                        <div class="list-group-item">
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1">Deposit #12344</h6>
                                <small>5 hours ago</small>
                            </div>
                            <p class="mb-1">3 checks deposited - $8,500.00</p>
                        </div>
                    </div>
                `;
            }, 1000);

            // Load pending actions
            setTimeout(() => {
                document.getElementById('pendingActions').innerHTML = `
                    <div class="list-group">
                        <div class="list-group-item">
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1">Checks awaiting collection</h6>
                                <span class="badge bg-warning">12</span>
                            </div>
                            <p class="mb-1">12 checks in deposits ready for collection</p>
                        </div>
                        <div class="list-group-item">
                            <div class="d-flex w-100 justify-content-between">
                                <h6 class="mb-1">Overdue checks</h6>
                                <span class="badge bg-danger">3</span>
                            </div>
                            <p class="mb-1">3 checks past due date</p>
                        </div>
                    </div>
                `;
            }, 1500);
        });

        // Auto-refresh dashboard every 5 minutes
        setInterval(refreshDashboard, 300000);
    </script>
}
