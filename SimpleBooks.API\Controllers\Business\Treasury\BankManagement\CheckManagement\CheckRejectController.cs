﻿namespace SimpleBooks.API.Controllers.Business.Treasury.BankManagement.CheckManagement
{
    public class CheckRejectController : BaseBusinessController<CheckRejectModel, CheckRejectModel, CreateCheckRejectViewModel, UpdateCheckRejectViewModel>
    {
        private readonly ICheckRejectService _checkRejectService;

        public CheckRejectController(ICheckRejectService checkRejectService) : base(checkRejectService)
        {
            _checkRejectService = checkRejectService;
        }
    }
}
