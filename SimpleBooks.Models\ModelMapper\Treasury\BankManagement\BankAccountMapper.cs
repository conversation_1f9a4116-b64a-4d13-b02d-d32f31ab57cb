﻿namespace SimpleBooks.Models.ModelMapper.Treasury.BankManagement
{
    public static class BankAccountMapper
    {
        public static CreateBankAccountViewModel ToCreateDto(this BankAccountModel entity)
        {
            CreateBankAccountViewModel viewModel = new CreateBankAccountViewModel()
            {
                BankAccountNumber = entity.BankAccountNumber,
                BankAccountCurrency = entity.BankAccountCurrency,
                BankAccountIBAN = entity.BankAccountIBAN,
                BankAccountSwiftCode = entity.BankAccountSwiftCode,
            };
            return viewModel;
        }

        public static BankAccountModel ToEntity(this CreateBankAccountViewModel entity)
        {
            BankAccountModel model = new BankAccountModel()
            {
                BankAccountNumber = entity.BankAccountNumber,
                BankAccountCurrency = entity.BankAccountCurrency,
                BankAccountIBAN = entity.BankAccountIBAN,
                BankAccountSwiftCode = entity.BankAccountSwiftCode,
            };
            return model;
        }

        public static UpdateBankAccountViewModel ToUpdateDto(this BankAccountModel entity)
        {
            UpdateBankAccountViewModel viewModel = new UpdateBankAccountViewModel()
            {
                Id = entity.Id,
                BankAccountNumber = entity.BankAccountNumber,
                BankAccountCurrency = entity.BankAccountCurrency,
                BankAccountIBAN = entity.BankAccountIBAN,
                BankAccountSwiftCode = entity.BankAccountSwiftCode,
                BankId = entity.BankId,
            };
            return viewModel;
        }

        public static BankAccountModel ToEntity(this UpdateBankAccountViewModel entity)
        {
            BankAccountModel model = new BankAccountModel()
            {
                Id = entity.Id,
                BankAccountNumber = entity.BankAccountNumber,
                BankAccountCurrency = entity.BankAccountCurrency,
                BankAccountIBAN = entity.BankAccountIBAN,
                BankAccountSwiftCode = entity.BankAccountSwiftCode,
                BankId = entity.BankId,
            };
            return model;
        }
    }
}
