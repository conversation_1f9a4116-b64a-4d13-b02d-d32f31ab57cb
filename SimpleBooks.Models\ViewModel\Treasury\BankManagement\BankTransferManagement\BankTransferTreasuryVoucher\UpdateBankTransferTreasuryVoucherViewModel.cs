﻿namespace SimpleBooks.Models.ViewModel.Treasury.BankManagement.BankTransferManagement.BankTransferTreasuryVoucher
{
    public class UpdateBankTransferTreasuryVoucherViewModel : UpdateTreasuryVoucherViewModel, IEntityMapper<BankTransferTreasuryVoucherModel, UpdateBankTransferTreasuryVoucherViewModel>
    {
        [CustomRequired]
        [DisplayName("Bank")]
        public Ulid BankId { get; set; }
        [CustomRequired]
        [DisplayName("Bank Account")]
        public Ulid BankAccountId { get; set; }

        public UpdateBankTransferTreasuryVoucherViewModel ToDto(BankTransferTreasuryVoucherModel entity) => entity.ToUpdateDto();

        public BankTransferTreasuryVoucherModel ToEntity() => BankTransferTreasuryVoucherMapper.ToEntity(this);
    }
}
