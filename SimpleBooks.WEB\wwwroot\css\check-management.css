/* Check Management System Styles */

/* Dashboard Cards */
.check-status-card {
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
    cursor: pointer;
}

.check-status-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.check-status-card .card-body {
    padding: 1.5rem;
}

.check-status-card h3 {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

/* Status Badges */
.status-badge {
    font-size: 0.875rem;
    padding: 0.375rem 0.75rem;
    border-radius: 0.375rem;
}

.badge-received {
    background-color: #17a2b8;
    color: white;
}

.badge-deposited {
    background-color: #ffc107;
    color: #212529;
}

.badge-collected {
    background-color: #28a745;
    color: white;
}

.badge-rejected {
    background-color: #dc3545;
    color: white;
}

.badge-cleared {
    background-color: #28a745;
    color: white;
}

/* Check Tables */
.check-table {
    font-size: 0.9rem;
}

.check-table th {
    background-color: #f8f9fa;
    border-top: none;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.8rem;
    letter-spacing: 0.5px;
}

.check-table td {
    vertical-align: middle;
    padding: 0.75rem;
}

.check-table .check-number {
    font-family: 'Courier New', monospace;
    font-weight: bold;
    color: #495057;
}

.check-table .amount {
    font-weight: bold;
    color: #28a745;
    text-align: right;
}

.check-table .issuer-name {
    max-width: 150px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* Action Buttons */
.action-buttons {
    display: flex;
    gap: 0.25rem;
    flex-wrap: wrap;
}

.action-buttons .btn {
    font-size: 0.8rem;
    padding: 0.25rem 0.5rem;
}

/* Quick Actions */
.quick-action-card {
    border: 2px solid transparent;
    transition: all 0.3s ease;
    height: 100%;
}

.quick-action-card:hover {
    border-color: #007bff;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.15);
}

.quick-action-card .btn {
    border: none;
    background: none;
    color: inherit;
    height: 100%;
    width: 100%;
    padding: 1.5rem;
    text-decoration: none;
}

.quick-action-card .btn:hover {
    background: none;
    color: inherit;
}

.quick-action-card i {
    font-size: 2rem;
    margin-bottom: 0.5rem;
    display: block;
}

/* Modals */
.modal-header.bg-danger {
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.modal-header.bg-success {
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.modal-body .alert {
    border-radius: 0.5rem;
}

/* Forms */
.form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
}

.form-control:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.form-select:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Check Selection */
.check-selection-row {
    transition: background-color 0.2s ease;
}

.check-selection-row:hover {
    background-color: #f8f9fa;
}

.check-selection-row.selected {
    background-color: #e3f2fd;
}

.check-selection-row input[type="checkbox"] {
    transform: scale(1.2);
}

/* Progress Indicators */
.progress-step {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
}

.progress-step .step-number {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background-color: #6c757d;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    margin-right: 1rem;
}

.progress-step.active .step-number {
    background-color: #007bff;
}

.progress-step.completed .step-number {
    background-color: #28a745;
}

/* Notifications */
#notification-container {
    z-index: 9999;
    max-width: 400px;
}

#notification-container .alert {
    margin-bottom: 0.5rem;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    border: none;
}

/* Loading States */
.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.btn.loading {
    position: relative;
    color: transparent;
}

.btn.loading::after {
    content: "";
    position: absolute;
    width: 16px;
    height: 16px;
    top: 50%;
    left: 50%;
    margin-left: -8px;
    margin-top: -8px;
    border: 2px solid transparent;
    border-top-color: currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive Design */
@media (max-width: 768px) {
    .check-table {
        font-size: 0.8rem;
    }
    
    .check-table .issuer-name {
        max-width: 100px;
    }
    
    .action-buttons {
        flex-direction: column;
    }
    
    .action-buttons .btn {
        width: 100%;
        margin-bottom: 0.25rem;
    }
    
    .quick-action-card .btn {
        padding: 1rem;
    }
    
    .quick-action-card i {
        font-size: 1.5rem;
    }
}

@media (max-width: 576px) {
    .check-status-card h3 {
        font-size: 1.5rem;
    }
    
    .modal-dialog {
        margin: 0.5rem;
    }
    
    .table-responsive {
        border: none;
    }
}

/* Print Styles */
@media print {
    .btn, .action-buttons, .modal, .navbar, .sidebar {
        display: none !important;
    }
    
    .check-table {
        font-size: 12px;
    }
    
    .card {
        border: 1px solid #000;
        box-shadow: none;
    }
    
    .badge {
        border: 1px solid #000;
        color: #000 !important;
        background: transparent !important;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .check-table th {
        background-color: #343a40;
        color: #fff;
    }
    
    .check-selection-row:hover {
        background-color: #495057;
    }
    
    .check-selection-row.selected {
        background-color: #1e3a5f;
    }
    
    .form-label {
        color: #e9ecef;
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}

/* Utility Classes */
.text-monospace {
    font-family: 'Courier New', monospace;
}

.cursor-pointer {
    cursor: pointer;
}

.border-dashed {
    border-style: dashed !important;
}

.bg-light-blue {
    background-color: #e3f2fd;
}

.text-muted-light {
    color: #6c757d;
}

/* Custom Scrollbar */
.custom-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: #6c757d #f8f9fa;
}

.custom-scrollbar::-webkit-scrollbar {
    width: 8px;
}

.custom-scrollbar::-webkit-scrollbar-track {
    background: #f8f9fa;
    border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
    background: #6c757d;
    border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: #495057;
}
