﻿namespace SimpleBooks.Models.ViewModel.Treasury.BankManagement.Bank
{
    public class UpdateBankViewModel : BaseUpdateViewModel, IEntityMapper<BankModel, UpdateBankViewModel>
    {
        [CustomRequired]
        [DisplayN<PERSON>("Bank Name")]
        public string BankName { get; set; }
        [Display<PERSON><PERSON>("Bank Identifier Code")]
        public string? BankIdentifier { get; set; }

        [CustomRequired]
        [DisplayName("Bank Accounts")]
        public IList<UpdateBankAccountViewModel> BankAccounts { get; set; } = new List<UpdateBankAccountViewModel>();

        public UpdateBankViewModel ToDto(BankModel entity) => entity.ToUpdateDto();

        public BankModel ToEntity() => BankMapper.ToEntity(this);
    }
}
