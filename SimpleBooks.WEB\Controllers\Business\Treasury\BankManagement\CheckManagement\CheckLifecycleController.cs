using Microsoft.AspNetCore.Mvc;
using SimpleBooks.Models.Domain.Treasury.CheckManagement;
using SimpleBooks.Models.Model.Treasury.BankManagement.CheckManagement;
using SimpleBooks.Services.Server.Business.Treasury.BankManagement.CheckManagement;
using SimpleBooks.WEB.Controllers.Business.Base;

namespace SimpleBooks.WEB.Controllers.Business.Treasury.BankManagement.CheckManagement
{
    public class CheckLifecycleController : Controller
    {
        private readonly ICheckLifecycleService _checkLifecycleService;
        private readonly ICheckCollectionService _checkCollectionService;
        private readonly ICheckRejectService _checkRejectService;

        public CheckLifecycleController(
            ICheckLifecycleService checkLifecycleService,
            ICheckCollectionService checkCollectionService,
            ICheckRejectService checkRejectService)
        {
            _checkLifecycleService = checkLifecycleService;
            _checkCollectionService = checkCollectionService;
            _checkRejectService = checkRejectService;
        }

        /// <summary>
        /// Check management dashboard
        /// </summary>
        public async Task<IActionResult> Index()
        {
            try
            {
                // This would typically load summary data for the dashboard
                var viewModel = new CheckManagementDashboardViewModel();
                return View(viewModel);
            }
            catch (Exception ex)
            {
                TempData["Error"] = $"Error loading dashboard: {ex.Message}";
                return View(new CheckManagementDashboardViewModel());
            }
        }

        /// <summary>
        /// View for collecting checks
        /// </summary>
        public async Task<IActionResult> CollectChecks(Ulid depositId)
        {
            try
            {
                var availableChecks = await _checkCollectionService.GetChecksAvailableForCollectionAsync(depositId);
                
                var viewModel = new CollectChecksViewModel
                {
                    DepositId = depositId,
                    AvailableChecks = availableChecks.Data ?? new List<CheckTreasuryVoucherModel>()
                };

                return View(viewModel);
            }
            catch (Exception ex)
            {
                TempData["Error"] = $"Error loading checks for collection: {ex.Message}";
                return RedirectToAction("Index", "CheckDeposit");
            }
        }

        /// <summary>
        /// Collect a check
        /// </summary>
        [HttpPost]
        public async Task<IActionResult> CollectCheck(Ulid checkId, Ulid depositId, DateTime? collectionDate = null)
        {
            try
            {
                var collection = new CheckCollectionModel
                {
                    Id = Ulid.NewUlid(),
                    CollectionDate = collectionDate ?? DateTime.Today,
                    CheckDepositId = depositId,
                    CheckTreasuryVoucherId = checkId
                };

                var result = await _checkCollectionService.CollectCheckAsync(checkId, collection);

                if (result.IsSuccess)
                {
                    TempData["Success"] = "Check collected successfully.";
                }
                else
                {
                    TempData["Error"] = result.Message;
                }
            }
            catch (Exception ex)
            {
                TempData["Error"] = $"Error collecting check: {ex.Message}";
            }

            return RedirectToAction(nameof(CollectChecks), new { depositId });
        }

        /// <summary>
        /// View for rejecting checks
        /// </summary>
        public async Task<IActionResult> RejectChecks(Ulid depositId)
        {
            try
            {
                var availableChecks = await _checkRejectService.GetChecksAvailableForRejectionAsync(depositId);
                
                var viewModel = new RejectChecksViewModel
                {
                    DepositId = depositId,
                    AvailableChecks = availableChecks.Data ?? new List<CheckTreasuryVoucherModel>()
                };

                return View(viewModel);
            }
            catch (Exception ex)
            {
                TempData["Error"] = $"Error loading checks for rejection: {ex.Message}";
                return RedirectToAction("Index", "CheckDeposit");
            }
        }

        /// <summary>
        /// Reject a check
        /// </summary>
        [HttpPost]
        public async Task<IActionResult> RejectCheck(Ulid checkId, Ulid depositId, string reason, DateTime? rejectDate = null)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(reason))
                {
                    TempData["Error"] = "Rejection reason is required.";
                    return RedirectToAction(nameof(RejectChecks), new { depositId });
                }

                var rejection = new CheckRejectModel
                {
                    Id = Ulid.NewUlid(),
                    RejectDate = rejectDate ?? DateTime.Today,
                    RefranceNumber = reason,
                    CheckDepositId = depositId,
                    CheckTreasuryVoucherId = checkId
                };

                var result = await _checkRejectService.RejectCheckAsync(checkId, rejection);

                if (result.IsSuccess)
                {
                    TempData["Success"] = "Check rejected successfully.";
                }
                else
                {
                    TempData["Error"] = result.Message;
                }
            }
            catch (Exception ex)
            {
                TempData["Error"] = $"Error rejecting check: {ex.Message}";
            }

            return RedirectToAction(nameof(RejectChecks), new { depositId });
        }

        /// <summary>
        /// View check details with audit trail
        /// </summary>
        public async Task<IActionResult> CheckDetails(Ulid checkId)
        {
            try
            {
                var result = await _checkLifecycleService.GetCheckAsync(checkId);
                
                if (!result.IsSuccess)
                {
                    TempData["Error"] = result.Message;
                    return RedirectToAction("Index", "CheckTreasuryVoucher");
                }

                var viewModel = new CheckDetailsViewModel
                {
                    Check = result.Data
                };

                return View(viewModel);
            }
            catch (Exception ex)
            {
                TempData["Error"] = $"Error loading check details: {ex.Message}";
                return RedirectToAction("Index", "CheckTreasuryVoucher");
            }
        }

        /// <summary>
        /// AJAX endpoint to get check status
        /// </summary>
        [HttpGet]
        public async Task<JsonResult> GetCheckStatus(Ulid checkId)
        {
            try
            {
                var result = await _checkLifecycleService.GetCheckAsync(checkId);
                
                if (result.IsSuccess)
                {
                    var check = result.Data;
                    return Json(new
                    {
                        success = true,
                        data = new
                        {
                            check.Id,
                            check.CheckNumber,
                            Status = check.Status.Name,
                            check.Amount,
                            CanBeModified = check.CanBeModified(),
                            IsInDeposit = check.IsInDeposit(),
                            CanBeRemovedFromDeposit = check.Status.CanBeRemovedFromDeposit()
                        }
                    });
                }
                else
                {
                    return Json(new { success = false, message = result.Message });
                }
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = ex.Message });
            }
        }

        /// <summary>
        /// AJAX endpoint to validate checks for deposit
        /// </summary>
        [HttpPost]
        public async Task<JsonResult> ValidateChecksForDeposit([FromBody] List<Ulid> checkIds)
        {
            try
            {
                var checks = new List<Check>();
                foreach (var checkId in checkIds)
                {
                    var checkResult = await _checkLifecycleService.GetCheckAsync(checkId);
                    if (checkResult.IsSuccess)
                        checks.Add(checkResult.Data);
                }

                var validation = CheckBusinessRules.ValidateBulkDeposit(checks);

                return Json(new
                {
                    success = true,
                    isValid = validation.IsValid,
                    errors = validation.Errors,
                    validChecks = checks.Count(c => c.Status == CheckStatus.Received),
                    totalChecks = checks.Count
                });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = ex.Message });
            }
        }

        /// <summary>
        /// AJAX endpoint to get checks in deposit
        /// </summary>
        [HttpGet]
        public async Task<JsonResult> GetChecksInDeposit(Ulid depositId)
        {
            try
            {
                var result = await _checkLifecycleService.GetChecksInDepositAsync(depositId);
                
                if (result.IsSuccess)
                {
                    var checks = result.Data.Select(c => new
                    {
                        c.Id,
                        c.CheckNumber,
                        c.Amount,
                        c.IssuerName,
                        Status = c.Status.Name,
                        CanBeRemoved = c.Status.CanBeRemovedFromDeposit(),
                        IsFinalStatus = c.Status.IsFinalStatus(),
                        AmountFormatted = c.Amount.ToString("C")
                    });

                    return Json(new { success = true, data = checks });
                }
                else
                {
                    return Json(new { success = false, message = result.Message });
                }
            }
            catch (Exception ex)
            {
                return Json(new { success = false, message = ex.Message });
            }
        }
    }

    // View Models
    public class CheckManagementDashboardViewModel
    {
        public int TotalChecks { get; set; }
        public int ReceivedChecks { get; set; }
        public int DepositedChecks { get; set; }
        public int CollectedChecks { get; set; }
        public int RejectedChecks { get; set; }
        public decimal TotalAmount { get; set; }
    }

    public class CollectChecksViewModel
    {
        public Ulid DepositId { get; set; }
        public List<CheckTreasuryVoucherModel> AvailableChecks { get; set; } = new();
    }

    public class RejectChecksViewModel
    {
        public Ulid DepositId { get; set; }
        public List<CheckTreasuryVoucherModel> AvailableChecks { get; set; } = new();
    }

    public class CheckDetailsViewModel
    {
        public Check Check { get; set; } = null!;
    }

    public class DepositDetailsViewModel
    {
        public CheckDepositModel Deposit { get; set; } = null!;
        public DepositSummary Summary { get; set; } = null!;
        public List<CheckTreasuryVoucherModel> RemovableChecks { get; set; } = new();
    }
}
