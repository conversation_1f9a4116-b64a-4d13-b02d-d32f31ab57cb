﻿namespace SimpleBooks.Models.ModelMapper.Treasury.CashManagement
{
    public static class CashTreasuryVoucherMapper
    {
        public static CreateCashTreasuryVoucherViewModel ToCreateDto(this CashTreasuryVoucherModel entity)
        {
            CreateCashTreasuryVoucherViewModel viewModel = new CreateCashTreasuryVoucherViewModel()
            {
                TransactionDate = entity.TransactionDate,
                Amount = entity.Amount,
                Note = entity.Note,
                RefranceNumber = entity.RefranceNumber,
                TransactionTypeId = entity.TransactionTypeId,
                BeneficiaryTypeId = entity.BeneficiaryTypeId,
                BeneficiaryId = entity.GetBeneficiaryId(),
                DrawerId = entity.DrawerId,
                DrawerLocationId = entity.DrawerLocationId,
                TreasuryLines = entity.TreasuryLines.Select(x => x.ToCreateDto()).ToList(),
            };
            return viewModel;
        }

        public static CashTreasuryVoucherModel ToEntity(this CreateCashTreasuryVoucherViewModel entity)
        {
            CashTreasuryVoucherModel model = new CashTreasuryVoucherModel()
            {
                TransactionDate = entity.TransactionDate,
                Amount = entity.Amount,
                Note = entity.Note,
                RefranceNumber = entity.RefranceNumber,
                TransactionTypeId = entity.TransactionTypeId,
                BeneficiaryTypeId = entity.BeneficiaryTypeId,
                VendorId = entity.BeneficiaryTypeId == BeneficiaryTypeEnumeration.Vendor.Value ? entity.BeneficiaryId : null,
                CustomerId = entity.BeneficiaryTypeId == BeneficiaryTypeEnumeration.Customer.Value ? entity.BeneficiaryId : null,
                EmployeeId = entity.BeneficiaryTypeId == BeneficiaryTypeEnumeration.Employee.Value ? entity.BeneficiaryId : null,
                DrawerId = entity.DrawerId,
                DrawerLocationId = entity.DrawerLocationId,
                TreasuryLines = entity.TreasuryLines.Select(x => x.ToEntity()).ToList(),
            };
            return model;
        }

        public static UpdateCashTreasuryVoucherViewModel ToUpdateDto(this CashTreasuryVoucherModel entity)
        {
            UpdateCashTreasuryVoucherViewModel viewModel = new UpdateCashTreasuryVoucherViewModel()
            {
                Id = entity.Id,
                TransactionDate = entity.TransactionDate,
                VoucherSerial = entity.VoucherSerial,
                TVID = entity.TVID,
                Amount = entity.Amount,
                Note = entity.Note,
                RefranceNumber = entity.RefranceNumber,
                TransactionTypeId = entity.TransactionTypeId,
                BeneficiaryTypeId = entity.BeneficiaryTypeId,
                BeneficiaryId = entity.GetBeneficiaryId(),
                DrawerId = entity.DrawerId,
                DrawerLocationId = entity.DrawerLocationId,
                TreasuryLines = entity.TreasuryLines.Select(x => x.ToUpdateDto()).ToList(),
            };
            return viewModel;
        }

        public static CashTreasuryVoucherModel ToEntity(this UpdateCashTreasuryVoucherViewModel entity)
        {
            CashTreasuryVoucherModel model = new CashTreasuryVoucherModel()
            {
                Id = entity.Id,
                TransactionDate = entity.TransactionDate,
                VoucherSerial = entity.VoucherSerial,
                TVID = entity.TVID,
                Amount = entity.Amount,
                Note = entity.Note,
                RefranceNumber = entity.RefranceNumber,
                TransactionTypeId = entity.TransactionTypeId,
                BeneficiaryTypeId = entity.BeneficiaryTypeId,
                VendorId = entity.BeneficiaryTypeId == BeneficiaryTypeEnumeration.Vendor.Value ? entity.BeneficiaryId : null,
                CustomerId = entity.BeneficiaryTypeId == BeneficiaryTypeEnumeration.Customer.Value ? entity.BeneficiaryId : null,
                EmployeeId = entity.BeneficiaryTypeId == BeneficiaryTypeEnumeration.Employee.Value ? entity.BeneficiaryId : null,
                DrawerId = entity.DrawerId,
                DrawerLocationId = entity.DrawerLocationId,
                TreasuryLines = entity.TreasuryLines.Select(x => x.ToEntity()).ToList(),
            };
            return model;
        }

        public static Ulid GetBeneficiaryId(this CashTreasuryVoucherModel entity)
        {
            if (entity.VendorId.HasValue)
                return entity.VendorId.Value;
            else if (entity.CustomerId.HasValue)
                return entity.CustomerId.Value;
            else if (entity.EmployeeId.HasValue)
                return entity.EmployeeId.Value;
            throw new InvalidOperationException("No beneficiary ID found.");
        }
    }
}
