﻿namespace SimpleBooks.Models.ModelMapper.Treasury.BankManagement.CheckManagement
{
    public static class CheckStatusHistoryMapper
    {
        public static CreateCheckStatusHistoryViewModel ToCreateDto(this CheckStatusHistoryModel entity)
        {
            CreateCheckStatusHistoryViewModel viewModel = new CreateCheckStatusHistoryViewModel()
            {
                TransactionDate = entity.TransactionDate,
                Note = entity.Note,
                CheckStatusFromId = entity.CheckStatusFromId,
                CheckStatusToId = entity.CheckStatusToId,
                CheckTreasuryVoucherId = entity.CheckTreasuryVoucherId,
                CheckDepositId = entity.CheckDepositId,
                CheckCollectionId = entity.CheckCollectionId,
                CheckRejectId = entity.CheckRejectId,
            };
            return viewModel;
        }

        public static CheckStatusHistoryModel ToEntity(this CreateCheckStatusHistoryViewModel entity)
        {
            CheckStatusHistoryModel model = new CheckStatusHistoryModel()
            {
                TransactionDate = entity.TransactionDate,
                Note = entity.Note,
                CheckStatusFromId = entity.CheckStatusFromId,
                CheckStatusToId = entity.CheckStatusToId,
                CheckTreasuryVoucherId = entity.CheckTreasuryVoucherId,
                CheckDepositId = entity.CheckDepositId,
                CheckCollectionId = entity.CheckCollectionId,
                CheckRejectId = entity.CheckRejectId,
            };
            return model;
        }

        public static UpdateCheckStatusHistoryViewModel ToUpdateDto(this CheckStatusHistoryModel entity)
        {
            UpdateCheckStatusHistoryViewModel viewModel = new UpdateCheckStatusHistoryViewModel()
            {
                Id = entity.Id,
                TransactionDate = entity.TransactionDate,
                Note = entity.Note,
                CheckStatusFromId = entity.CheckStatusFromId,
                CheckStatusToId = entity.CheckStatusToId,
                CheckTreasuryVoucherId = entity.CheckTreasuryVoucherId,
                CheckDepositId = entity.CheckDepositId,
                CheckCollectionId = entity.CheckCollectionId,
                CheckRejectId = entity.CheckRejectId,
            };
            return viewModel;
        }

        public static CheckStatusHistoryModel ToEntity(this UpdateCheckStatusHistoryViewModel entity)
        {
            CheckStatusHistoryModel model = new CheckStatusHistoryModel()
            {
                Id = entity.Id,
                TransactionDate = entity.TransactionDate,
                Note = entity.Note,
                CheckStatusFromId = entity.CheckStatusFromId,
                CheckStatusToId = entity.CheckStatusToId,
                CheckTreasuryVoucherId = entity.CheckTreasuryVoucherId,
                CheckDepositId = entity.CheckDepositId,
                CheckCollectionId = entity.CheckCollectionId,
                CheckRejectId = entity.CheckRejectId,
            };
            return model;
        }
    }
}
