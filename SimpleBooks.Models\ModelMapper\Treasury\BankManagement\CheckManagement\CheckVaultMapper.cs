﻿namespace SimpleBooks.Models.ModelMapper.Treasury.BankManagement.CheckManagement
{
    public static class CheckVaultMapper
    {
        public static CreateCheckVaultViewModel ToCreateDto(this CheckVaultModel entity)
        {
            CreateCheckVaultViewModel viewModel = new CreateCheckVaultViewModel()
            {
                CheckVaultName = entity.CheckVaultName,
                CheckVaultLocations = entity.CheckVaultLocations.Select(x => x.ToCreateDto()).ToList(),
            };
            return viewModel;
        }

        public static CheckVaultModel ToEntity(this CreateCheckVaultViewModel entity)
        {
            CheckVaultModel model = new CheckVaultModel()
            {
                CheckVaultName = entity.CheckVaultName,
                CheckVaultLocations = entity.CheckVaultLocations.Select(x => x.ToEntity()).ToList(),
            };
            return model;
        }

        public static UpdateCheckVaultViewModel ToUpdateDto(this CheckVaultModel entity)
        {
            UpdateCheckVaultViewModel viewModel = new UpdateCheckVaultViewModel()
            {
                Id = entity.Id,
                CheckVaultName = entity.CheckVaultName,
                CheckVaultLocations = entity.CheckVaultLocations.Select(x => x.ToUpdateDto()).ToList(),
            };
            return viewModel;
        }

        public static CheckVaultModel ToEntity(this UpdateCheckVaultViewModel entity)
        {
            CheckVaultModel model = new CheckVaultModel()
            {
                Id = entity.Id,
                CheckVaultName = entity.CheckVaultName,
                CheckVaultLocations = entity.CheckVaultLocations.Select(x => x.ToEntity()).ToList(),
            };
            return model;
        }
    }
}
