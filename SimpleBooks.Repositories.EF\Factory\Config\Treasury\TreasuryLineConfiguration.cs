﻿namespace SimpleBooks.Repositories.EF.Factory.Config.Treasury
{
    public class TreasuryLineConfiguration : IEntityTypeConfiguration<TreasuryLineModel>
    {
        public void Configure(EntityTypeBuilder<TreasuryLineModel> builder)
        {
            builder.<PERSON><PERSON>ey(x => new { x.Id });

            builder.HasOne(d => d.Expenses).WithMany(d => d.TreasuryLines)
                .HasForeignKey(d => d.ExpensesId)
                .OnDelete(DeleteBehavior.ClientCascade)
                .IsRequired(false);

            builder.HasOne(d => d.Bill).WithMany(d => d.TreasuryLines)
                .HasForeignKey(d => d.BillId)
                .OnDelete(DeleteBehavior.ClientCascade)
                .IsRequired(false);

            builder.HasOne(d => d.BillReturn).WithMany(d => d.TreasuryLines)
                .HasForeignKey(d => d.BillReturnId)
                .OnDelete(DeleteBehavior.ClientCascade)
                .IsRequired(false);

            builder.HasOne(d => d.Invoice).WithMany(d => d.TreasuryLines)
                .HasForeignKey(d => d.InvoiceId)
                .OnDelete(DeleteBehavior.ClientCascade)
                .IsRequired(false);

            builder.HasOne(d => d.InvoiceReturn).WithMany(d => d.TreasuryLines)
                .HasForeignKey(d => d.InvoiceReturnId)
                .OnDelete(DeleteBehavior.ClientCascade)
                .IsRequired(false);

            builder.HasOne(d => d.CashTreasuryVoucher).WithMany(p => p.TreasuryLines)
                .HasForeignKey(d => d.CashTreasuryVoucherId)
                .OnDelete(DeleteBehavior.ClientCascade)
                .IsRequired(false);

            builder.HasOne(d => d.CheckTreasuryVoucher).WithMany(p => p.TreasuryLines)
                .HasForeignKey(d => d.CheckTreasuryVoucherId)
                .OnDelete(DeleteBehavior.ClientCascade)
                .IsRequired(false);

            builder.HasOne(d => d.BankTransferTreasuryVoucher).WithMany(p => p.TreasuryLines)
                .HasForeignKey(d => d.BankTransferTreasuryVoucherId)
                .OnDelete(DeleteBehavior.ClientCascade)
                .IsRequired(false);
        }
    }
}
