﻿namespace SimpleBooks.Repositories.EF.Factory.Config.Treasury.CashManagement
{
    public class DrawerLocationConfiguration : IEntityTypeConfiguration<DrawerLocationModel>
    {
        public void Configure(EntityTypeBuilder<DrawerLocationModel> builder)
        {
            builder.<PERSON><PERSON>ey(x => new { x.Id });

            builder.HasOne(d => d.Drawer).WithMany(p => p.DrawerLocations)
                .HasForeignKey(d => d.DrawerId)
                .OnDelete(DeleteBehavior.ClientCascade);
        }
    }
}
