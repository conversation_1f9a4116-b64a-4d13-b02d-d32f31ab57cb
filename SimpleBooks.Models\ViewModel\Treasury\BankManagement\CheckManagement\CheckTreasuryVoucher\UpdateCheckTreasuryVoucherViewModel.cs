﻿namespace SimpleBooks.Models.ViewModel.Treasury.BankManagement.CheckManagement.CheckTreasuryVoucher
{
    public class UpdateCheckTreasuryVoucherViewModel : UpdateTreasuryVoucherViewModel, IEntityMapper<CheckTreasuryVoucherModel, UpdateCheckTreasuryVoucherViewModel>
    {
        [CustomRequired]
        [DisplayName("Check Number")]
        public string CheckNumber { get; set; }
        [CustomRequired]
        [DisplayName("Due Date")]
        [Column(TypeName = "Date"), DataType(DataType.Date)]
        public DateTime DueDate { get; set; }
        [DisplayName("Issuer Name")]
        public string IssuerName { get; set; } // For received checks
        [DisplayName("Bearer Name")]
        public string BearerName { get; set; } // For issued checks
        [CustomRequired]
        [DisplayName("Check Status")]
        public Ulid CheckStatusId { get; set; }

        [CustomRequired]
        [DisplayName("Check Vault")]
        public Ulid CheckVaultId { get; set; }

        [CustomRequired]
        [DisplayName("Check Vault Location")]
        public Ulid CheckVaultLocationId { get; set; }

        [DisplayName("Check Status Histories")]
        public IList<UpdateCheckStatusHistoryViewModel> CheckStatusHistories { get; set; } = new List<UpdateCheckStatusHistoryViewModel>();

        public UpdateCheckTreasuryVoucherViewModel ToDto(CheckTreasuryVoucherModel entity) => entity.ToUpdateDto();

        public CheckTreasuryVoucherModel ToEntity() => CheckTreasuryVoucherMapper.ToEntity(this);
    }
}
