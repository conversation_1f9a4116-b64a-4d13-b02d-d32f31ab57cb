﻿namespace SimpleBooks.Models.ViewModel.Treasury.BankManagement.CheckManagement.CheckCollection
{
    public class UpdateCheckCollectionViewModel : BaseUpdateViewModel, IEntityMapper<CheckCollectionModel, UpdateCheckCollectionViewModel>
    {
        [CustomRequired]
        [DisplayName("Collection Date")]
        [Column(TypeName = "Date"), DataType(DataType.Date)]
        public DateTime CollectionDate { get; set; }
        [CustomRequired]
        [DisplayName("Check Deposit")]
        public Ulid CheckDepositId { get; set; }
        [CustomRequired]
        [DisplayName("Check Treasury Voucher")]
        public Ulid CheckTreasuryVoucherId { get; set; }

        [CustomRequired]
        [DisplayName("Check Status Histories")]
        public virtual ICollection<UpdateCheckStatusHistoryViewModel> CheckStatusHistories { get; set; } = new List<UpdateCheckStatusHistoryViewModel>();

        public UpdateCheckCollectionViewModel ToDto(CheckCollectionModel entity) => entity.ToUpdateDto();

        public CheckCollectionModel ToEntity() => CheckCollectionMapper.ToEntity(this);
    }
}
