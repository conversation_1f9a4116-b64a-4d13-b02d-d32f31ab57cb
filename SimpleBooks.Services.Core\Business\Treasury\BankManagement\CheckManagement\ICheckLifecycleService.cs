﻿using SimpleBooks.Models.Domain.Treasury.CheckManagement;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SimpleBooks.Services.Core.Business.Treasury.BankManagement.CheckManagement
{
    public interface ICheckLifecycleService
    {
        Task<ServiceResult<List<Check>>> DepositChecksAsync(List<Ulid> checkIds, CheckDepositModel deposit);
        Task<ServiceResult<Check>> RemoveCheckFromDepositAsync(Ulid checkId, Ulid depositId, string reason = "");
        Task<ServiceResult<Check>> CollectCheckAsync(Ulid checkId, CheckCollectionModel collection);
        Task<ServiceResult<Check>> RejectCheckAsync(Ulid checkId, CheckRejectModel rejection);
        Task<ServiceResult<Check>> GetCheckAsync(Ulid checkId);
        Task<ServiceResult<List<Check>>> GetChecksInDepositAsync(Ulid depositId);
    }
}
