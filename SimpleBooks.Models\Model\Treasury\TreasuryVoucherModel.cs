﻿namespace SimpleBooks.Models.Model.Treasury
{
    [NotMapped]
    [TypeDescriptionProvider(typeof(BaseTypeDescriptionProvider<TreasuryVoucherModel>))]
    public partial class TreasuryVoucherModel : BaseWithoutTrackingModel
    {
        [CustomRequired]
        [DisplayName("Transaction Date")]
        [Column(TypeName = "Date"), DataType(DataType.Date)]
        public DateTime TransactionDate { get; set; }
        [CustomRequired]
        [DisplayName("Transaction Serial")]
        public int VoucherSerial { get; set; }
        [CustomRequired]
        [DisplayName("TVID")]
        public int TVID { get; set; }
        [CustomRequired]
        [DisplayName("Amount")]
        public decimal Amount { get; set; }
        [DisplayName("Note")]
        public string Note { get; set; }
        [DisplayName("Refrance Number")]
        public string? RefranceNumber { get; set; }

        [CustomRequired]
        [DisplayName("Transaction Type")]
        public Ulid TransactionTypeId { get; set; }
        public virtual TransactionTypeModel? TransactionType { get; set; }

        [CustomRequired]
        [DisplayName("Beneficiary Type")]
        public Ulid BeneficiaryTypeId { get; set; }
        public virtual BeneficiaryTypeModel? BeneficiaryType { get; set; }

        [DisplayName("Vendor")]
        public Ulid? VendorId { get; set; }
        public virtual VendorModel? Vendor { get; set; }

        [DisplayName("Customer")]
        public Ulid? CustomerId { get; set; }
        public virtual CustomerModel? Customer { get; set; }

        [DisplayName("Employee")]
        public Ulid? EmployeeId { get; set; }
        public virtual EmployeeModel? Employee { get; set; }

        [CustomRequired]
        [DisplayName("Treasury Lines")]
        public virtual ICollection<TreasuryLineModel> TreasuryLines { get; set; } = new List<TreasuryLineModel>();
    }
}
