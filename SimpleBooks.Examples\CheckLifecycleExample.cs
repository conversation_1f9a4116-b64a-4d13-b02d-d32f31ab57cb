using SimpleBooks.Models.Domain.Treasury.CheckManagement;
using SimpleBooks.Models.Model.Treasury.BankManagement.CheckManagement;
using SimpleBooks.Services.Server.Business.Treasury.BankManagement.CheckManagement;
using SimpleBooks.Repositories.Core.IFactory;

namespace SimpleBooks.Examples
{
    /// <summary>
    /// Example demonstrating the proper usage of the refactored check management system
    /// </summary>
    public class CheckLifecycleExample
    {
        private readonly ICheckLifecycleService _checkLifecycleService;
        private readonly IUnitOfWork _unitOfWork;

        public CheckLifecycleExample(ICheckLifecycleService checkLifecycleService, IUnitOfWork unitOfWork)
        {
            _checkLifecycleService = checkLifecycleService;
            _unitOfWork = unitOfWork;
        }

        /// <summary>
        /// Example: Complete check lifecycle from creation to collection
        /// </summary>
        public async Task<bool> CompleteCheckLifecycleExample()
        {
            try
            {
                // Step 1: Create checks (normally done through CheckTreasuryVoucherService)
                var checkIds = new List<Ulid>
                {
                    await <PERSON><PERSON><PERSON><PERSON><PERSON>he<PERSON>("CHK001", 1000m),
                    await <PERSON><PERSON><PERSON><PERSON><PERSON>he<PERSON>("CHK002", 2000m),
                    await CreateSampleCheck("CHK003", 1500m)
                };

                // Step 2: Create a deposit and deposit the checks
                var deposit = new CheckDepositModel
                {
                    Id = Ulid.NewUlid(),
                    DepositDate = DateTime.Today,
                    BankId = Ulid.NewUlid(), // Sample bank ID
                    BankAccountId = Ulid.NewUlid(), // Sample account ID
                    CheckTreasuryVouchers = new List<CheckTreasuryVoucherModel>()
                };

                // Add check references to deposit
                foreach (var checkId in checkIds)
                {
                    deposit.CheckTreasuryVouchers.Add(new CheckTreasuryVoucherModel { Id = checkId });
                }

                var depositResult = await _checkLifecycleService.DepositChecksAsync(checkIds, deposit);
                if (!depositResult.IsSuccess)
                {
                    Console.WriteLine($"Failed to deposit checks: {depositResult.Message}");
                    return false;
                }

                Console.WriteLine($"Successfully deposited {depositResult.Data.Count} checks");

                // Step 3: Collect some checks
                var firstCheckId = checkIds[0];
                var collection = new CheckCollectionModel
                {
                    Id = Ulid.NewUlid(),
                    CollectionDate = DateTime.Today.AddDays(1),
                    CheckDepositId = deposit.Id,
                    CheckTreasuryVoucherId = firstCheckId
                };

                var collectionResult = await _checkLifecycleService.CollectCheckAsync(firstCheckId, collection);
                if (!collectionResult.IsSuccess)
                {
                    Console.WriteLine($"Failed to collect check: {collectionResult.Message}");
                    return false;
                }

                Console.WriteLine($"Successfully collected check {firstCheckId}");

                // Step 4: Reject a check
                var secondCheckId = checkIds[1];
                var rejection = new CheckRejectModel
                {
                    Id = Ulid.NewUlid(),
                    RejectDate = DateTime.Today.AddDays(1),
                    RefranceNumber = "Insufficient funds",
                    CheckDepositId = deposit.Id,
                    CheckTreasuryVoucherId = secondCheckId
                };

                var rejectionResult = await _checkLifecycleService.RejectCheckAsync(secondCheckId, rejection);
                if (!rejectionResult.IsSuccess)
                {
                    Console.WriteLine($"Failed to reject check: {rejectionResult.Message}");
                    return false;
                }

                Console.WriteLine($"Successfully rejected check {secondCheckId}");

                // Step 5: Try to remove a check from deposit (should fail for collected/rejected checks)
                var removeResult1 = await _checkLifecycleService.RemoveCheckFromDepositAsync(firstCheckId, deposit.Id, "Test removal");
                Console.WriteLine($"Attempt to remove collected check: {removeResult1.Message}");

                // Step 6: Remove the remaining deposited check (should succeed)
                var thirdCheckId = checkIds[2];
                var removeResult2 = await _checkLifecycleService.RemoveCheckFromDepositAsync(thirdCheckId, deposit.Id, "Business decision");
                if (removeResult2.IsSuccess)
                {
                    Console.WriteLine($"Successfully removed check {thirdCheckId} from deposit");
                }

                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in check lifecycle example: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Example: Bulk operations with validation
        /// </summary>
        public async Task<bool> BulkOperationsExample()
        {
            try
            {
                // Create multiple checks
                var checkIds = new List<Ulid>();
                for (int i = 1; i <= 10; i++)
                {
                    checkIds.Add(await CreateSampleCheck($"BULK{i:D3}", 1000m * i));
                }

                // Create deposit
                var deposit = new CheckDepositModel
                {
                    Id = Ulid.NewUlid(),
                    DepositDate = DateTime.Today,
                    BankId = Ulid.NewUlid(),
                    BankAccountId = Ulid.NewUlid(),
                    CheckTreasuryVouchers = checkIds.Select(id => new CheckTreasuryVoucherModel { Id = id }).ToList()
                };

                // Deposit all checks at once
                var bulkDepositResult = await _checkLifecycleService.DepositChecksAsync(checkIds, deposit);
                if (!bulkDepositResult.IsSuccess)
                {
                    Console.WriteLine($"Bulk deposit failed: {bulkDepositResult.Message}");
                    return false;
                }

                Console.WriteLine($"Bulk deposited {bulkDepositResult.Data.Count} checks");

                // Get deposit summary
                var summary = await _unitOfWork.CheckLifecycle.GetDepositSummaryAsync(deposit.Id);
                Console.WriteLine($"Deposit Summary:");
                Console.WriteLine($"  Total Checks: {summary.TotalChecks}");
                Console.WriteLine($"  Total Amount: {summary.TotalAmount:C}");
                Console.WriteLine($"  Deposited: {summary.DepositedChecks}");
                Console.WriteLine($"  Collected: {summary.CollectedChecks}");
                Console.WriteLine($"  Rejected: {summary.RejectedChecks}");

                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in bulk operations example: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Example: Error handling and validation
        /// </summary>
        public async Task<bool> ValidationExample()
        {
            try
            {
                // Try to deposit a non-existent check
                var invalidCheckIds = new List<Ulid> { Ulid.NewUlid() };
                var deposit = new CheckDepositModel
                {
                    Id = Ulid.NewUlid(),
                    DepositDate = DateTime.Today,
                    BankId = Ulid.NewUlid(),
                    BankAccountId = Ulid.NewUlid(),
                    CheckTreasuryVouchers = new List<CheckTreasuryVoucherModel>()
                };

                var invalidDepositResult = await _checkLifecycleService.DepositChecksAsync(invalidCheckIds, deposit);
                Console.WriteLine($"Expected failure for invalid check: {invalidDepositResult.Message}");

                // Try to collect a check that's not deposited
                var checkId = await CreateSampleCheck("VAL001", 1000m);
                var collection = new CheckCollectionModel
                {
                    Id = Ulid.NewUlid(),
                    CollectionDate = DateTime.Today,
                    CheckDepositId = Ulid.NewUlid(),
                    CheckTreasuryVoucherId = checkId
                };

                var invalidCollectionResult = await _checkLifecycleService.CollectCheckAsync(checkId, collection);
                Console.WriteLine($"Expected failure for non-deposited check collection: {invalidCollectionResult.Message}");

                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error in validation example: {ex.Message}");
                return false;
            }
        }

        private async Task<Ulid> CreateSampleCheck(string checkNumber, decimal amount)
        {
            var check = new CheckTreasuryVoucherModel
            {
                Id = Ulid.NewUlid(),
                CheckNumber = checkNumber,
                DueDate = DateTime.Today.AddDays(30),
                IssuerName = "Sample Issuer",
                BearerName = "Sample Bearer",
                Amount = amount,
                CheckStatusId = CheckStatusEnumeration.Received.Value,
                TransactionTypeId = TransactionTypeEnumeration.TreasuryCheckIn.Value,
                TransactionDate = DateTime.Today,
                Note = $"Sample check {checkNumber}",
                CheckVaultId = Ulid.NewUlid(),
                CheckVaultLocationId = Ulid.NewUlid()
            };

            await _unitOfWork.CheckTreasuryVoucher.AddAsync(check);
            _unitOfWork.Save();

            return check.Id;
        }
    }
}
