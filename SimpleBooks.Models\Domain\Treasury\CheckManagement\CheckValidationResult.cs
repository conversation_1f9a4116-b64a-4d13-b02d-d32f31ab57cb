namespace SimpleBooks.Models.Domain.Treasury.CheckManagement
{
    /// <summary>
    /// Result of check validation operations
    /// </summary>
    public class CheckValidationResult
    {
        public bool IsValid { get; private set; }
        public List<string> Errors { get; private set; } = new();

        private CheckValidationResult(bool isValid)
        {
            IsValid = isValid;
        }

        public static CheckValidationResult Success() => new(true);

        public static CheckValidationResult Failure(string error)
        {
            var result = new CheckValidationResult(false);
            result.Errors.Add(error);
            return result;
        }

        public static CheckValidationResult Failure(IEnumerable<string> errors)
        {
            var result = new CheckValidationResult(false);
            result.Errors.AddRange(errors);
            return result;
        }

        public void AddError(string error)
        {
            IsValid = false;
            Errors.Add(error);
        }

        public string GetErrorMessage() => string.Join(Environment.NewLine, Errors);
    }

    /// <summary>
    /// Business rules for check operations
    /// </summary>
    public static class CheckBusinessRules
    {
        public static CheckValidationResult ValidateDeposit(Check check, Ulid depositId)
        {
            var errors = new List<string>();

            if (check.Status != CheckStatus.Received)
                errors.Add($"Only checks in 'Received' status can be deposited. Current status: {check.Status.Name}");

            if (check.DepositId.HasValue)
                errors.Add("Check is already deposited");

            if (depositId == Ulid.Empty)
                errors.Add("Deposit ID cannot be empty");

            return errors.Any() ? CheckValidationResult.Failure(errors) : CheckValidationResult.Success();
        }

        public static CheckValidationResult ValidateRemovalFromDeposit(Check check)
        {
            var errors = new List<string>();

            if (check.Status != CheckStatus.Deposited)
                errors.Add($"Only checks in 'Deposited' status can be removed from deposit. Current status: {check.Status.Name}");

            if (!check.DepositId.HasValue)
                errors.Add("Check is not currently deposited");

            if (check.Status.IsFinalStatus())
                errors.Add($"Cannot remove check in final status '{check.Status.Name}' from deposit");

            return errors.Any() ? CheckValidationResult.Failure(errors) : CheckValidationResult.Success();
        }

        public static CheckValidationResult ValidateCollection(Check check, Ulid collectionId)
        {
            var errors = new List<string>();

            if (check.Status != CheckStatus.Deposited)
                errors.Add($"Only deposited checks can be collected. Current status: {check.Status.Name}");

            if (!check.DepositId.HasValue)
                errors.Add("Check must be deposited before collection");

            if (collectionId == Ulid.Empty)
                errors.Add("Collection ID cannot be empty");

            return errors.Any() ? CheckValidationResult.Failure(errors) : CheckValidationResult.Success();
        }

        public static CheckValidationResult ValidateRejection(Check check, Ulid rejectId, string reason)
        {
            var errors = new List<string>();

            if (check.Status != CheckStatus.Deposited)
                errors.Add($"Only deposited checks can be rejected. Current status: {check.Status.Name}");

            if (!check.DepositId.HasValue)
                errors.Add("Check must be deposited before rejection");

            if (rejectId == Ulid.Empty)
                errors.Add("Reject ID cannot be empty");

            if (string.IsNullOrWhiteSpace(reason))
                errors.Add("Rejection reason is required");

            return errors.Any() ? CheckValidationResult.Failure(errors) : CheckValidationResult.Success();
        }

        public static CheckValidationResult ValidateStatusTransition(CheckStatus fromStatus, CheckStatus toStatus, Ulid transactionType)
        {
            if (!fromStatus.CanTransitionTo(toStatus, transactionType))
            {
                var transactionTypeName = transactionType == TransactionTypeEnumeration.TreasuryCheckIn.Value 
                    ? "Received Check" 
                    : "Issued Check";
                
                return CheckValidationResult.Failure(
                    $"Invalid status transition from '{fromStatus.Name}' to '{toStatus.Name}' for {transactionTypeName}");
            }

            return CheckValidationResult.Success();
        }

        public static CheckValidationResult ValidateCheckForModification(Check check)
        {
            if (check.Status.IsFinalStatus())
                return CheckValidationResult.Failure($"Check in '{check.Status.Name}' status cannot be modified");

            return CheckValidationResult.Success();
        }

        public static CheckValidationResult ValidateBulkDeposit(IEnumerable<Check> checks)
        {
            var errors = new List<string>();
            var checkList = checks.ToList();

            if (!checkList.Any())
                errors.Add("At least one check must be selected for deposit");

            foreach (var check in checkList)
            {
                var validation = ValidateDeposit(check, Ulid.NewUlid()); // Temporary ID for validation
                if (!validation.IsValid)
                    errors.Add($"Check {check.CheckNumber}: {validation.GetErrorMessage()}");
            }

            // Validate all checks are for the same transaction type
            var transactionTypes = checkList.Select(c => c.TransactionTypeId).Distinct().ToList();
            if (transactionTypes.Count > 1)
                errors.Add("All checks in a deposit must have the same transaction type");

            return errors.Any() ? CheckValidationResult.Failure(errors) : CheckValidationResult.Success();
        }
    }
}
