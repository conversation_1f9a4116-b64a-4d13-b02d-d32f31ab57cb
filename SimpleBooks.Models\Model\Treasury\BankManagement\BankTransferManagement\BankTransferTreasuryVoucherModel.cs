﻿namespace SimpleBooks.Models.Model.Treasury.BankManagement.BankTransferManagement
{
    [Table("BankTransferTreasuryVoucher")]
    [TypeDescriptionProvider(typeof(BaseTypeDescriptionProvider<BankTransferTreasuryVoucherModel>))]
    public partial class BankTransferTreasuryVoucherModel : TreasuryVoucherModel
    {
        [CustomRequired]
        [DisplayName("Bank")]
        public Ulid BankId { get; set; }
        public virtual BankModel? Bank { get; set; }

        [CustomRequired]
        [DisplayName("Bank Account")]
        public Ulid BankAccountId { get; set; }
        public virtual BankAccountModel? BankAccount { get; set; }
    }
}
