﻿namespace SimpleBooks.Models.ViewModel.Treasury.CashManagement.CashTreasuryVoucher
{
    public class UpdateCashTreasuryVoucherViewModel : UpdateTreasuryVoucherViewModel, IEntityMapper<CashTreasuryVoucherModel, UpdateCashTreasuryVoucherViewModel>
    {
        [CustomRequired]
        [DisplayName("Drawer")]
        public Ulid DrawerId { get; set; }

        [CustomRequired]
        [DisplayName("Drawer Location")]
        public Ulid DrawerLocationId { get; set; }

        public UpdateCashTreasuryVoucherViewModel ToDto(CashTreasuryVoucherModel entity) => entity.ToUpdateDto();

        public CashTreasuryVoucherModel ToEntity() => CashTreasuryVoucherMapper.ToEntity(this);
    }
}
