﻿namespace SimpleBooks.Models.ViewModel.Treasury.BankManagement.BankAccount
{
    public class CreateBankAccountViewModel : BaseCreateViewModel, IEntityMapper<BankAccountModel, CreateBankAccountViewModel>
    {
        [CustomRequired]
        [DisplayName("Bank Account Number")]
        public string BankAccountNumber { get; set; }
        [CustomRequired]
        [DisplayName("Bank Account Currency")]
        public string BankAccountCurrency { get; set; }
        [DisplayName("Bank Account IBAN")]
        public string? BankAccountIBAN { get; set; }
        [DisplayName("Bank Account Swift Code")]
        public string? BankAccountSwiftCode { get; set; }

        public CreateBankAccountViewModel ToDto(BankAccountModel entity) => entity.ToCreateDto();

        public BankAccountModel ToEntity() => BankAccountMapper.ToEntity(this);
    }
}
