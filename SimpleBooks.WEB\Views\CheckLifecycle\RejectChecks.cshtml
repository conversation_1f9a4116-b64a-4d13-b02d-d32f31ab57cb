@using SimpleBooks.WEB.Controllers.Business.Treasury.BankManagement.CheckManagement
@model RejectChecksViewModel
@{
    ViewData["Title"] = "Reject Checks";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h4 class="card-title mb-0">
                        <i class="bi bi-x-circle"></i> Reject Checks
                    </h4>
                    <div>
                        <a href="@Url.Action("Details", "CheckDeposit", new { id = Model.DepositId })" class="btn btn-secondary">
                            <i class="bi bi-arrow-left"></i> Back to Deposit
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    @if (Model.AvailableChecks.Any())
                    {
                        <div class="alert alert-warning">
                            <i class="bi bi-exclamation-triangle"></i>
                            <strong>Warning:</strong> Rejecting checks will mark them as rejected and they cannot be collected. 
                            Please provide a clear reason for rejection.
                        </div>

                        <div class="table-responsive">
                            <table class="table table-striped" id="checksTable">
                                <thead class="table-dark">
                                    <tr>
                                        <th>Check Number</th>
                                        <th>Issuer</th>
                                        <th>Bearer</th>
                                        <th>Amount</th>
                                        <th>Due Date</th>
                                        <th>Action</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (var check in Model.AvailableChecks)
                                    {
                                        <tr>
                                            <td>@check.CheckNumber</td>
                                            <td>@check.IssuerName</td>
                                            <td>@check.BearerName</td>
                                            <td>@check.Amount.ToString("C")</td>
                                            <td>@check.DueDate.ToString("dd/MM/yyyy")</td>
                                            <td>
                                                <button type="button" class="btn btn-sm btn-danger" 
                                                        onclick="showRejectModal('@check.Id', '@check.CheckNumber', '@check.Amount.ToString("C")')">
                                                    <i class="bi bi-x-circle"></i> Reject
                                                </button>
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                    }
                    else
                    {
                        <div class="alert alert-info">
                            <i class="bi bi-info-circle"></i>
                            No checks available for rejection in this deposit.
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Rejection Modal -->
<div class="modal fade" id="rejectModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title">
                    <i class="bi bi-x-circle"></i> Reject Check
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
            </div>
            <form id="rejectForm" method="post" action="@Url.Action("RejectCheck", "CheckLifecycle")">
                <div class="modal-body">
                    <input type="hidden" name="depositId" value="@Model.DepositId" />
                    <input type="hidden" name="checkId" id="rejectCheckId" />
                    
                    <div class="alert alert-warning">
                        <i class="bi bi-exclamation-triangle"></i>
                        <strong>Warning:</strong> This action cannot be undone. The check will be permanently marked as rejected.
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <strong>Check Number:</strong>
                            <p id="rejectCheckNumber" class="text-primary"></p>
                        </div>
                        <div class="col-md-6">
                            <strong>Amount:</strong>
                            <p id="rejectCheckAmount" class="text-success"></p>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="rejectDate" class="form-label">Rejection Date: <span class="text-danger">*</span></label>
                        <input type="date" class="form-control" id="rejectDate" name="rejectDate" 
                               value="@DateTime.Today.ToString("yyyy-MM-dd")" required>
                    </div>

                    <div class="mb-3">
                        <label for="rejectReason" class="form-label">Reason for Rejection: <span class="text-danger">*</span></label>
                        <select class="form-select" id="rejectReasonSelect" onchange="handleReasonChange()">
                            <option value="">Select a reason...</option>
                            <option value="Insufficient funds">Insufficient funds</option>
                            <option value="Account closed">Account closed</option>
                            <option value="Invalid signature">Invalid signature</option>
                            <option value="Post-dated check">Post-dated check</option>
                            <option value="Stop payment">Stop payment</option>
                            <option value="Damaged check">Damaged check</option>
                            <option value="Other">Other (specify below)</option>
                        </select>
                    </div>

                    <div class="mb-3">
                        <label for="rejectReason" class="form-label">Additional Details:</label>
                        <textarea class="form-control" id="rejectReason" name="reason" rows="4" 
                                  placeholder="Provide detailed reason for rejection..." required></textarea>
                        <div class="form-text">
                            Please provide a clear and detailed reason for rejecting this check. 
                            This information will be recorded in the audit trail.
                        </div>
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="confirmReject" required>
                            <label class="form-check-label" for="confirmReject">
                                I confirm that I want to reject this check and understand this action cannot be undone.
                            </label>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                        <i class="bi bi-x"></i> Cancel
                    </button>
                    <button type="submit" class="btn btn-danger" id="confirmRejectBtn" disabled>
                        <i class="bi bi-x-circle"></i> Reject Check
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        function showRejectModal(checkId, checkNumber, checkAmount) {
            document.getElementById('rejectCheckId').value = checkId;
            document.getElementById('rejectCheckNumber').textContent = checkNumber;
            document.getElementById('rejectCheckAmount').textContent = checkAmount;
            
            // Reset form
            document.getElementById('rejectForm').reset();
            document.getElementById('rejectDate').value = '@DateTime.Today.ToString("yyyy-MM-dd")';
            document.getElementById('confirmRejectBtn').disabled = true;
            
            new bootstrap.Modal(document.getElementById('rejectModal')).show();
        }

        function handleReasonChange() {
            const select = document.getElementById('rejectReasonSelect');
            const textarea = document.getElementById('rejectReason');
            
            if (select.value && select.value !== 'Other') {
                textarea.value = select.value;
            } else if (select.value === 'Other') {
                textarea.value = '';
                textarea.focus();
            }
            
            validateForm();
        }

        function validateForm() {
            const reason = document.getElementById('rejectReason').value.trim();
            const confirm = document.getElementById('confirmReject').checked;
            const submitBtn = document.getElementById('confirmRejectBtn');
            
            submitBtn.disabled = !(reason.length > 0 && confirm);
        }

        // Add event listeners
        document.getElementById('rejectReason').addEventListener('input', validateForm);
        document.getElementById('confirmReject').addEventListener('change', validateForm);

        // Form submission handling
        document.getElementById('rejectForm').addEventListener('submit', function(e) {
            const reason = document.getElementById('rejectReason').value.trim();
            
            if (reason.length < 10) {
                e.preventDefault();
                alert('Please provide a more detailed reason for rejection (at least 10 characters).');
                return false;
            }

            // Show loading state
            const submitBtn = document.getElementById('confirmRejectBtn');
            submitBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> Processing...';
            submitBtn.disabled = true;
        });

        // Initialize DataTable if available
        $(document).ready(function() {
            if ($.fn.DataTable) {
                $('#checksTable').DataTable({
                    responsive: true,
                    pageLength: 25,
                    order: [[0, 'asc']] // Sort by check number
                });
            }
        });
    </script>
}
