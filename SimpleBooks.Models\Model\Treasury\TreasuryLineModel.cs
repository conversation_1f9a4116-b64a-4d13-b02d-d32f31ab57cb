﻿namespace SimpleBooks.Models.Model.Treasury
{
    [Table("TreasuryLine")]
    [TypeDescriptionProvider(typeof(BaseTypeDescriptionProvider<TreasuryLineModel>))]
    public class TreasuryLineModel : BaseWithoutTrackingModel
    {
        [CustomRequired]
        [DisplayName("Amount")]
        public decimal Amount { get; set; }

        [CustomRequired]
        [DisplayName("Treasury Line Type")]
        public Ulid TreasuryLineTypeId { get; set; }
        public virtual TreasuryLineTypeModel? TreasuryLine { get; set; }

        [DisplayName("Expenses")]
        public Ulid? ExpensesId { get; set; }
        public virtual ExpensesModel? Expenses { get; set; }

        [DisplayName("Bill")]
        public Ulid? BillId { get; set; }
        public virtual BillModel? Bill { get; set; }

        [DisplayName("BillReturn")]
        public Ulid? BillReturnId { get; set; }
        public virtual BillReturnModel? BillReturn { get; set; }

        [DisplayName("Invoice")]
        public Ulid? InvoiceId { get; set; }
        public virtual InvoiceModel? Invoice { get; set; }

        [DisplayName("InvoiceReturn")]
        public Ulid? InvoiceReturnId { get; set; }
        public virtual InvoiceReturnModel? InvoiceReturn { get; set; }

        [DisplayName("Cash Treasury Voucher")]
        public Ulid? CashTreasuryVoucherId { get; set; }
        [JsonIgnore]
        public virtual CashTreasuryVoucherModel? CashTreasuryVoucher { get; set; }

        [DisplayName("Check Treasury Voucher")]
        public Ulid? CheckTreasuryVoucherId { get; set; }
        [JsonIgnore]
        public virtual CheckTreasuryVoucherModel? CheckTreasuryVoucher { get; set; }

        [DisplayName("Bank Transfer Treasury Voucher")]
        public Ulid? BankTransferTreasuryVoucherId { get; set; }
        [JsonIgnore]
        public virtual BankTransferTreasuryVoucherModel? BankTransferTreasuryVoucher { get; set; }
    }
}
