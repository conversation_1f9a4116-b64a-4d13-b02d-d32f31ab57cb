﻿using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace SimpleBooks.Repositories.EF.Migrations.MySQLMigrations
{
    /// <inheritdoc />
    public partial class SimpleBooks_007 : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_CheckCheckTreasuryVoucher_BeneficiaryType_BeneficiaryTypeId",
                table: "CheckCheckTreasuryVoucher");

            migrationBuilder.DropForeignKey(
                name: "FK_CheckCheckTreasuryVoucher_CheckDeposit_CheckDepositId",
                table: "CheckCheckTreasuryVoucher");

            migrationBuilder.DropForeignKey(
                name: "FK_CheckCheckTreasuryVoucher_CheckStatus_CheckStatusId",
                table: "CheckCheckTreasuryVoucher");

            migrationBuilder.DropForeignKey(
                name: "FK_CheckCheckTreasuryVoucher_CheckVaultLocation_CheckVaultLocat~",
                table: "CheckCheckTreasuryVoucher");

            migrationBuilder.DropForeignKey(
                name: "FK_CheckCheckTreasuryVoucher_CheckVault_CheckVaultId",
                table: "CheckCheckTreasuryVoucher");

            migrationBuilder.DropForeignKey(
                name: "FK_CheckCheckTreasuryVoucher_Customer_CustomerId",
                table: "CheckCheckTreasuryVoucher");

            migrationBuilder.DropForeignKey(
                name: "FK_CheckCheckTreasuryVoucher_Employee_EmployeeId",
                table: "CheckCheckTreasuryVoucher");

            migrationBuilder.DropForeignKey(
                name: "FK_CheckCheckTreasuryVoucher_TransactionType_TransactionTypeId",
                table: "CheckCheckTreasuryVoucher");

            migrationBuilder.DropForeignKey(
                name: "FK_CheckCheckTreasuryVoucher_Vendor_VendorId",
                table: "CheckCheckTreasuryVoucher");

            migrationBuilder.DropForeignKey(
                name: "FK_CheckCollection_CheckCheckTreasuryVoucher_CheckTreasuryVouch~",
                table: "CheckCollection");

            migrationBuilder.DropForeignKey(
                name: "FK_CheckReject_CheckCheckTreasuryVoucher_CheckTreasuryVoucherId",
                table: "CheckReject");

            migrationBuilder.DropForeignKey(
                name: "FK_CheckStatusHistory_CheckCheckTreasuryVoucher_CheckTreasuryVo~",
                table: "CheckStatusHistory");

            migrationBuilder.DropForeignKey(
                name: "FK_TreasuryLine_CheckCheckTreasuryVoucher_CheckTreasuryVoucherId",
                table: "TreasuryLine");

            migrationBuilder.DropPrimaryKey(
                name: "PK_CheckCheckTreasuryVoucher",
                table: "CheckCheckTreasuryVoucher");

            migrationBuilder.RenameTable(
                name: "CheckCheckTreasuryVoucher",
                newName: "CheckTreasuryVoucher");

            migrationBuilder.RenameIndex(
                name: "IX_CheckCheckTreasuryVoucher_VendorId",
                table: "CheckTreasuryVoucher",
                newName: "IX_CheckTreasuryVoucher_VendorId");

            migrationBuilder.RenameIndex(
                name: "IX_CheckCheckTreasuryVoucher_TransactionTypeId",
                table: "CheckTreasuryVoucher",
                newName: "IX_CheckTreasuryVoucher_TransactionTypeId");

            migrationBuilder.RenameIndex(
                name: "IX_CheckCheckTreasuryVoucher_EmployeeId",
                table: "CheckTreasuryVoucher",
                newName: "IX_CheckTreasuryVoucher_EmployeeId");

            migrationBuilder.RenameIndex(
                name: "IX_CheckCheckTreasuryVoucher_CustomerId",
                table: "CheckTreasuryVoucher",
                newName: "IX_CheckTreasuryVoucher_CustomerId");

            migrationBuilder.RenameIndex(
                name: "IX_CheckCheckTreasuryVoucher_CheckVaultLocationId",
                table: "CheckTreasuryVoucher",
                newName: "IX_CheckTreasuryVoucher_CheckVaultLocationId");

            migrationBuilder.RenameIndex(
                name: "IX_CheckCheckTreasuryVoucher_CheckVaultId",
                table: "CheckTreasuryVoucher",
                newName: "IX_CheckTreasuryVoucher_CheckVaultId");

            migrationBuilder.RenameIndex(
                name: "IX_CheckCheckTreasuryVoucher_CheckStatusId",
                table: "CheckTreasuryVoucher",
                newName: "IX_CheckTreasuryVoucher_CheckStatusId");

            migrationBuilder.RenameIndex(
                name: "IX_CheckCheckTreasuryVoucher_CheckDepositId",
                table: "CheckTreasuryVoucher",
                newName: "IX_CheckTreasuryVoucher_CheckDepositId");

            migrationBuilder.RenameIndex(
                name: "IX_CheckCheckTreasuryVoucher_BeneficiaryTypeId",
                table: "CheckTreasuryVoucher",
                newName: "IX_CheckTreasuryVoucher_BeneficiaryTypeId");

            migrationBuilder.AddPrimaryKey(
                name: "PK_CheckTreasuryVoucher",
                table: "CheckTreasuryVoucher",
                column: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_CheckCollection_CheckTreasuryVoucher_CheckTreasuryVoucherId",
                table: "CheckCollection",
                column: "CheckTreasuryVoucherId",
                principalTable: "CheckTreasuryVoucher",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_CheckReject_CheckTreasuryVoucher_CheckTreasuryVoucherId",
                table: "CheckReject",
                column: "CheckTreasuryVoucherId",
                principalTable: "CheckTreasuryVoucher",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_CheckStatusHistory_CheckTreasuryVoucher_CheckTreasuryVoucher~",
                table: "CheckStatusHistory",
                column: "CheckTreasuryVoucherId",
                principalTable: "CheckTreasuryVoucher",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_CheckTreasuryVoucher_BeneficiaryType_BeneficiaryTypeId",
                table: "CheckTreasuryVoucher",
                column: "BeneficiaryTypeId",
                principalTable: "BeneficiaryType",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_CheckTreasuryVoucher_CheckDeposit_CheckDepositId",
                table: "CheckTreasuryVoucher",
                column: "CheckDepositId",
                principalTable: "CheckDeposit",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_CheckTreasuryVoucher_CheckStatus_CheckStatusId",
                table: "CheckTreasuryVoucher",
                column: "CheckStatusId",
                principalTable: "CheckStatus",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_CheckTreasuryVoucher_CheckVaultLocation_CheckVaultLocationId",
                table: "CheckTreasuryVoucher",
                column: "CheckVaultLocationId",
                principalTable: "CheckVaultLocation",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_CheckTreasuryVoucher_CheckVault_CheckVaultId",
                table: "CheckTreasuryVoucher",
                column: "CheckVaultId",
                principalTable: "CheckVault",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_CheckTreasuryVoucher_Customer_CustomerId",
                table: "CheckTreasuryVoucher",
                column: "CustomerId",
                principalTable: "Customer",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_CheckTreasuryVoucher_Employee_EmployeeId",
                table: "CheckTreasuryVoucher",
                column: "EmployeeId",
                principalTable: "Employee",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_CheckTreasuryVoucher_TransactionType_TransactionTypeId",
                table: "CheckTreasuryVoucher",
                column: "TransactionTypeId",
                principalTable: "TransactionType",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_CheckTreasuryVoucher_Vendor_VendorId",
                table: "CheckTreasuryVoucher",
                column: "VendorId",
                principalTable: "Vendor",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_TreasuryLine_CheckTreasuryVoucher_CheckTreasuryVoucherId",
                table: "TreasuryLine",
                column: "CheckTreasuryVoucherId",
                principalTable: "CheckTreasuryVoucher",
                principalColumn: "Id");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_CheckCollection_CheckTreasuryVoucher_CheckTreasuryVoucherId",
                table: "CheckCollection");

            migrationBuilder.DropForeignKey(
                name: "FK_CheckReject_CheckTreasuryVoucher_CheckTreasuryVoucherId",
                table: "CheckReject");

            migrationBuilder.DropForeignKey(
                name: "FK_CheckStatusHistory_CheckTreasuryVoucher_CheckTreasuryVoucher~",
                table: "CheckStatusHistory");

            migrationBuilder.DropForeignKey(
                name: "FK_CheckTreasuryVoucher_BeneficiaryType_BeneficiaryTypeId",
                table: "CheckTreasuryVoucher");

            migrationBuilder.DropForeignKey(
                name: "FK_CheckTreasuryVoucher_CheckDeposit_CheckDepositId",
                table: "CheckTreasuryVoucher");

            migrationBuilder.DropForeignKey(
                name: "FK_CheckTreasuryVoucher_CheckStatus_CheckStatusId",
                table: "CheckTreasuryVoucher");

            migrationBuilder.DropForeignKey(
                name: "FK_CheckTreasuryVoucher_CheckVaultLocation_CheckVaultLocationId",
                table: "CheckTreasuryVoucher");

            migrationBuilder.DropForeignKey(
                name: "FK_CheckTreasuryVoucher_CheckVault_CheckVaultId",
                table: "CheckTreasuryVoucher");

            migrationBuilder.DropForeignKey(
                name: "FK_CheckTreasuryVoucher_Customer_CustomerId",
                table: "CheckTreasuryVoucher");

            migrationBuilder.DropForeignKey(
                name: "FK_CheckTreasuryVoucher_Employee_EmployeeId",
                table: "CheckTreasuryVoucher");

            migrationBuilder.DropForeignKey(
                name: "FK_CheckTreasuryVoucher_TransactionType_TransactionTypeId",
                table: "CheckTreasuryVoucher");

            migrationBuilder.DropForeignKey(
                name: "FK_CheckTreasuryVoucher_Vendor_VendorId",
                table: "CheckTreasuryVoucher");

            migrationBuilder.DropForeignKey(
                name: "FK_TreasuryLine_CheckTreasuryVoucher_CheckTreasuryVoucherId",
                table: "TreasuryLine");

            migrationBuilder.DropPrimaryKey(
                name: "PK_CheckTreasuryVoucher",
                table: "CheckTreasuryVoucher");

            migrationBuilder.RenameTable(
                name: "CheckTreasuryVoucher",
                newName: "CheckCheckTreasuryVoucher");

            migrationBuilder.RenameIndex(
                name: "IX_CheckTreasuryVoucher_VendorId",
                table: "CheckCheckTreasuryVoucher",
                newName: "IX_CheckCheckTreasuryVoucher_VendorId");

            migrationBuilder.RenameIndex(
                name: "IX_CheckTreasuryVoucher_TransactionTypeId",
                table: "CheckCheckTreasuryVoucher",
                newName: "IX_CheckCheckTreasuryVoucher_TransactionTypeId");

            migrationBuilder.RenameIndex(
                name: "IX_CheckTreasuryVoucher_EmployeeId",
                table: "CheckCheckTreasuryVoucher",
                newName: "IX_CheckCheckTreasuryVoucher_EmployeeId");

            migrationBuilder.RenameIndex(
                name: "IX_CheckTreasuryVoucher_CustomerId",
                table: "CheckCheckTreasuryVoucher",
                newName: "IX_CheckCheckTreasuryVoucher_CustomerId");

            migrationBuilder.RenameIndex(
                name: "IX_CheckTreasuryVoucher_CheckVaultLocationId",
                table: "CheckCheckTreasuryVoucher",
                newName: "IX_CheckCheckTreasuryVoucher_CheckVaultLocationId");

            migrationBuilder.RenameIndex(
                name: "IX_CheckTreasuryVoucher_CheckVaultId",
                table: "CheckCheckTreasuryVoucher",
                newName: "IX_CheckCheckTreasuryVoucher_CheckVaultId");

            migrationBuilder.RenameIndex(
                name: "IX_CheckTreasuryVoucher_CheckStatusId",
                table: "CheckCheckTreasuryVoucher",
                newName: "IX_CheckCheckTreasuryVoucher_CheckStatusId");

            migrationBuilder.RenameIndex(
                name: "IX_CheckTreasuryVoucher_CheckDepositId",
                table: "CheckCheckTreasuryVoucher",
                newName: "IX_CheckCheckTreasuryVoucher_CheckDepositId");

            migrationBuilder.RenameIndex(
                name: "IX_CheckTreasuryVoucher_BeneficiaryTypeId",
                table: "CheckCheckTreasuryVoucher",
                newName: "IX_CheckCheckTreasuryVoucher_BeneficiaryTypeId");

            migrationBuilder.AddPrimaryKey(
                name: "PK_CheckCheckTreasuryVoucher",
                table: "CheckCheckTreasuryVoucher",
                column: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_CheckCheckTreasuryVoucher_BeneficiaryType_BeneficiaryTypeId",
                table: "CheckCheckTreasuryVoucher",
                column: "BeneficiaryTypeId",
                principalTable: "BeneficiaryType",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_CheckCheckTreasuryVoucher_CheckDeposit_CheckDepositId",
                table: "CheckCheckTreasuryVoucher",
                column: "CheckDepositId",
                principalTable: "CheckDeposit",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_CheckCheckTreasuryVoucher_CheckStatus_CheckStatusId",
                table: "CheckCheckTreasuryVoucher",
                column: "CheckStatusId",
                principalTable: "CheckStatus",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_CheckCheckTreasuryVoucher_CheckVaultLocation_CheckVaultLocat~",
                table: "CheckCheckTreasuryVoucher",
                column: "CheckVaultLocationId",
                principalTable: "CheckVaultLocation",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_CheckCheckTreasuryVoucher_CheckVault_CheckVaultId",
                table: "CheckCheckTreasuryVoucher",
                column: "CheckVaultId",
                principalTable: "CheckVault",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_CheckCheckTreasuryVoucher_Customer_CustomerId",
                table: "CheckCheckTreasuryVoucher",
                column: "CustomerId",
                principalTable: "Customer",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_CheckCheckTreasuryVoucher_Employee_EmployeeId",
                table: "CheckCheckTreasuryVoucher",
                column: "EmployeeId",
                principalTable: "Employee",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_CheckCheckTreasuryVoucher_TransactionType_TransactionTypeId",
                table: "CheckCheckTreasuryVoucher",
                column: "TransactionTypeId",
                principalTable: "TransactionType",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_CheckCheckTreasuryVoucher_Vendor_VendorId",
                table: "CheckCheckTreasuryVoucher",
                column: "VendorId",
                principalTable: "Vendor",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_CheckCollection_CheckCheckTreasuryVoucher_CheckTreasuryVouch~",
                table: "CheckCollection",
                column: "CheckTreasuryVoucherId",
                principalTable: "CheckCheckTreasuryVoucher",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_CheckReject_CheckCheckTreasuryVoucher_CheckTreasuryVoucherId",
                table: "CheckReject",
                column: "CheckTreasuryVoucherId",
                principalTable: "CheckCheckTreasuryVoucher",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_CheckStatusHistory_CheckCheckTreasuryVoucher_CheckTreasuryVo~",
                table: "CheckStatusHistory",
                column: "CheckTreasuryVoucherId",
                principalTable: "CheckCheckTreasuryVoucher",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_TreasuryLine_CheckCheckTreasuryVoucher_CheckTreasuryVoucherId",
                table: "TreasuryLine",
                column: "CheckTreasuryVoucherId",
                principalTable: "CheckCheckTreasuryVoucher",
                principalColumn: "Id");
        }
    }
}
