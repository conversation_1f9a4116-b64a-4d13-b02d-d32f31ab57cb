﻿namespace SimpleBooks.Models.ViewModel.Treasury.BankManagement.CheckManagement.CheckCollection
{
    public class CreateCheckCollectionViewModel : BaseCreateViewModel, IEntityMapper<CheckCollectionModel, CreateCheckCollectionViewModel>
    {
        [CustomRequired]
        [DisplayName("Collection Date")]
        [Column(TypeName = "Date"), DataType(DataType.Date)]
        public DateTime CollectionDate { get; set; }
        [CustomRequired]
        [DisplayName("Check Deposit")]
        public Ulid CheckDepositId { get; set; }
        [CustomRequired]
        [DisplayName("Check Treasury Voucher")]
        public Ulid CheckTreasuryVoucherId { get; set; }

        [CustomRequired]
        [DisplayName("Check Status Histories")]
        public virtual ICollection<CreateCheckStatusHistoryViewModel> CheckStatusHistories { get; set; } = new List<CreateCheckStatusHistoryViewModel>();

        public CreateCheckCollectionViewModel ToDto(CheckCollectionModel entity) => entity.ToCreateDto();

        public CheckCollectionModel ToEntity() => CheckCollectionMapper.ToEntity(this);
    }
}
