﻿namespace SimpleBooks.Models.ViewModel.Treasury.BankManagement.BankAccount
{
    public class UpdateBankAccountViewModel : BaseUpdateViewModel, IEntityMapper<BankAccountModel, UpdateBankAccountViewModel>
    {
        [CustomRequired]
        [DisplayName("Bank Account Number")]
        public string BankAccountNumber { get; set; }
        [CustomRequired]
        [DisplayName("Bank Account Currency")]
        public string BankAccountCurrency { get; set; }
        [DisplayName("Bank Account IBAN")]
        public string? BankAccountIBAN { get; set; }
        [DisplayName("Bank Account Swift Code")]
        public string? BankAccountSwiftCode { get; set; }

        [CustomRequired]
        [DisplayName("Bank")]
        public Ulid BankId { get; set; }

        public UpdateBankAccountViewModel ToDto(BankAccountModel entity) => entity.ToUpdateDto();

        public BankAccountModel ToEntity() => BankAccountMapper.ToEntity(this);
    }
}
