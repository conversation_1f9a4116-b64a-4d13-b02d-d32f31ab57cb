﻿namespace SimpleBooks.Models.Model
{
    [Table("TransactionType")]
    [TypeDescriptionProvider(typeof(BaseTypeDescriptionProvider<TransactionTypeModel>))]
    public class TransactionTypeModel : BaseWithoutTrackingModel
    {
        [CustomRequired]
        [DisplayName("Transaction Type Name")]
        public string TransactionTypeName { get; set; }

        public virtual ICollection<InventoryModel> Inventories { get; set; } = new List<InventoryModel>();
        [DisplayName("Cash Treasury Vouchers")]
        public virtual ICollection<CashTreasuryVoucherModel> CashTreasuryVouchers { get; set; } = new List<CashTreasuryVoucherModel>();
        [DisplayName("Check Treasury Vouchers")]
        public virtual ICollection<CheckTreasuryVoucherModel> CheckTreasuryVouchers { get; set; } = new List<CheckTreasuryVoucherModel>();
        [DisplayName("Bank Transfer Treasury Vouchers")]
        public virtual ICollection<BankTransferTreasuryVoucherModel> BankTransferTreasuryVouchers { get; set; } = new List<BankTransferTreasuryVoucherModel>();
    }
}
