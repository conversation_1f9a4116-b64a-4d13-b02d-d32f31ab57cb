﻿namespace SimpleBooks.Models.ViewModel.Treasury.BankManagement.CheckManagement.CheckVault
{
    public class CreateCheckVaultViewModel : BaseCreateViewModel, IEntityMapper<CheckVaultModel, CreateCheckVaultViewModel>
    {
        [CustomRequired]
        [DisplayName("Check Vault Name")]
        public string CheckVaultName { get; set; }

        [CustomRequired]
        [DisplayName("CheckVault Locations")]
        public IList<CreateCheckVaultLocationViewModel> CheckVaultLocations { get; set; } = new List<CreateCheckVaultLocationViewModel>();

        public CreateCheckVaultViewModel ToDto(CheckVaultModel entity) => entity.ToCreateDto();

        public CheckVaultModel ToEntity() => CheckVaultMapper.ToEntity(this);
    }
}
