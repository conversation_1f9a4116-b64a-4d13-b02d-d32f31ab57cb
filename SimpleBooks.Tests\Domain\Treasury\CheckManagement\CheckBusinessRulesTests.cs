using SimpleBooks.Models.Domain.Treasury.CheckManagement;
using SimpleBooks.Models.Enumerations;
using Xunit;

namespace SimpleBooks.Tests.Domain.Treasury.CheckManagement
{
    public class CheckBusinessRulesTests
    {
        private Check CreateTestCheck(CheckStatus status = null)
        {
            var check = new Check(
                Ulid.NewUlid(),
                "CHK001",
                DateTime.Today.AddDays(30),
                "Test Issuer",
                "Test Bearer",
                1000m,
                TransactionTypeEnumeration.TreasuryCheckIn.Value,
                DateTime.Today,
                "Test check"
            );

            // Manually set status if needed for testing
            if (status != null && status != CheckStatus.Received)
            {
                if (status == CheckStatus.Deposited)
                {
                    check.DepositToBank(Ulid.NewUlid(), DateTime.Today);
                }
                else if (status == CheckStatus.Cleared)
                {
                    check.DepositToBank(Ulid.NewUlid(), DateTime.Today);
                    check.MarkAsCollected(Ulid.NewUlid(), DateTime.Today);
                }
                else if (status == CheckStatus.Rejected)
                {
                    check.DepositToBank(Ulid.NewUlid(), DateTime.Today);
                    check.MarkAsRejected(Ulid.NewUlid(), DateTime.Today, "Test rejection");
                }
            }

            return check;
        }

        [Fact]
        public void ValidateDeposit_ValidCheck_ShouldReturnSuccess()
        {
            // Arrange
            var check = CreateTestCheck(CheckStatus.Received);
            var depositId = Ulid.NewUlid();

            // Act
            var result = CheckBusinessRules.ValidateDeposit(check, depositId);

            // Assert
            Assert.True(result.IsValid);
            Assert.Empty(result.Errors);
        }

        [Fact]
        public void ValidateDeposit_CheckAlreadyDeposited_ShouldReturnFailure()
        {
            // Arrange
            var check = CreateTestCheck(CheckStatus.Deposited);
            var depositId = Ulid.NewUlid();

            // Act
            var result = CheckBusinessRules.ValidateDeposit(check, depositId);

            // Assert
            Assert.False(result.IsValid);
            Assert.Contains("already deposited", result.GetErrorMessage());
        }

        [Fact]
        public void ValidateDeposit_CheckNotInReceivedStatus_ShouldReturnFailure()
        {
            // Arrange
            var check = CreateTestCheck(CheckStatus.Cleared);
            var depositId = Ulid.NewUlid();

            // Act
            var result = CheckBusinessRules.ValidateDeposit(check, depositId);

            // Assert
            Assert.False(result.IsValid);
            Assert.Contains("Only checks in 'Received' status can be deposited", result.GetErrorMessage());
        }

        [Fact]
        public void ValidateDeposit_EmptyDepositId_ShouldReturnFailure()
        {
            // Arrange
            var check = CreateTestCheck(CheckStatus.Received);
            var depositId = Ulid.Empty;

            // Act
            var result = CheckBusinessRules.ValidateDeposit(check, depositId);

            // Assert
            Assert.False(result.IsValid);
            Assert.Contains("Deposit ID cannot be empty", result.GetErrorMessage());
        }

        [Fact]
        public void ValidateRemovalFromDeposit_ValidCheck_ShouldReturnSuccess()
        {
            // Arrange
            var check = CreateTestCheck(CheckStatus.Deposited);

            // Act
            var result = CheckBusinessRules.ValidateRemovalFromDeposit(check);

            // Assert
            Assert.True(result.IsValid);
            Assert.Empty(result.Errors);
        }

        [Fact]
        public void ValidateRemovalFromDeposit_CheckNotDeposited_ShouldReturnFailure()
        {
            // Arrange
            var check = CreateTestCheck(CheckStatus.Received);

            // Act
            var result = CheckBusinessRules.ValidateRemovalFromDeposit(check);

            // Assert
            Assert.False(result.IsValid);
            Assert.Contains("Only checks in 'Deposited' status can be removed", result.GetErrorMessage());
        }

        [Fact]
        public void ValidateRemovalFromDeposit_CheckInFinalStatus_ShouldReturnFailure()
        {
            // Arrange
            var check = CreateTestCheck(CheckStatus.Cleared);

            // Act
            var result = CheckBusinessRules.ValidateRemovalFromDeposit(check);

            // Assert
            Assert.False(result.IsValid);
            Assert.Contains("Cannot remove check in final status", result.GetErrorMessage());
        }

        [Fact]
        public void ValidateCollection_ValidCheck_ShouldReturnSuccess()
        {
            // Arrange
            var check = CreateTestCheck(CheckStatus.Deposited);
            var collectionId = Ulid.NewUlid();

            // Act
            var result = CheckBusinessRules.ValidateCollection(check, collectionId);

            // Assert
            Assert.True(result.IsValid);
            Assert.Empty(result.Errors);
        }

        [Fact]
        public void ValidateCollection_CheckNotDeposited_ShouldReturnFailure()
        {
            // Arrange
            var check = CreateTestCheck(CheckStatus.Received);
            var collectionId = Ulid.NewUlid();

            // Act
            var result = CheckBusinessRules.ValidateCollection(check, collectionId);

            // Assert
            Assert.False(result.IsValid);
            Assert.Contains("Only deposited checks can be collected", result.GetErrorMessage());
            Assert.Contains("Check must be deposited before collection", result.GetErrorMessage());
        }

        [Fact]
        public void ValidateRejection_ValidCheck_ShouldReturnSuccess()
        {
            // Arrange
            var check = CreateTestCheck(CheckStatus.Deposited);
            var rejectId = Ulid.NewUlid();
            var reason = "Insufficient funds";

            // Act
            var result = CheckBusinessRules.ValidateRejection(check, rejectId, reason);

            // Assert
            Assert.True(result.IsValid);
            Assert.Empty(result.Errors);
        }

        [Theory]
        [InlineData("")]
        [InlineData(null)]
        [InlineData("   ")]
        public void ValidateRejection_EmptyReason_ShouldReturnFailure(string reason)
        {
            // Arrange
            var check = CreateTestCheck(CheckStatus.Deposited);
            var rejectId = Ulid.NewUlid();

            // Act
            var result = CheckBusinessRules.ValidateRejection(check, rejectId, reason);

            // Assert
            Assert.False(result.IsValid);
            Assert.Contains("Rejection reason is required", result.GetErrorMessage());
        }

        [Fact]
        public void ValidateStatusTransition_ValidTransition_ShouldReturnSuccess()
        {
            // Arrange
            var fromStatus = CheckStatus.Received;
            var toStatus = CheckStatus.Deposited;
            var transactionType = TransactionTypeEnumeration.TreasuryCheckIn.Value;

            // Act
            var result = CheckBusinessRules.ValidateStatusTransition(fromStatus, toStatus, transactionType);

            // Assert
            Assert.True(result.IsValid);
            Assert.Empty(result.Errors);
        }

        [Fact]
        public void ValidateStatusTransition_InvalidTransition_ShouldReturnFailure()
        {
            // Arrange
            var fromStatus = CheckStatus.Received;
            var toStatus = CheckStatus.Cleared; // Invalid: can't go directly from Received to Cleared
            var transactionType = TransactionTypeEnumeration.TreasuryCheckIn.Value;

            // Act
            var result = CheckBusinessRules.ValidateStatusTransition(fromStatus, toStatus, transactionType);

            // Assert
            Assert.False(result.IsValid);
            Assert.Contains("Invalid status transition", result.GetErrorMessage());
        }

        [Fact]
        public void ValidateCheckForModification_ModifiableCheck_ShouldReturnSuccess()
        {
            // Arrange
            var check = CreateTestCheck(CheckStatus.Deposited);

            // Act
            var result = CheckBusinessRules.ValidateCheckForModification(check);

            // Assert
            Assert.True(result.IsValid);
            Assert.Empty(result.Errors);
        }

        [Fact]
        public void ValidateCheckForModification_FinalStatusCheck_ShouldReturnFailure()
        {
            // Arrange
            var check = CreateTestCheck(CheckStatus.Cleared);

            // Act
            var result = CheckBusinessRules.ValidateCheckForModification(check);

            // Assert
            Assert.False(result.IsValid);
            Assert.Contains("cannot be modified", result.GetErrorMessage());
        }

        [Fact]
        public void ValidateBulkDeposit_ValidChecks_ShouldReturnSuccess()
        {
            // Arrange
            var checks = new List<Check>
            {
                CreateTestCheck(CheckStatus.Received),
                CreateTestCheck(CheckStatus.Received),
                CreateTestCheck(CheckStatus.Received)
            };

            // Act
            var result = CheckBusinessRules.ValidateBulkDeposit(checks);

            // Assert
            Assert.True(result.IsValid);
            Assert.Empty(result.Errors);
        }

        [Fact]
        public void ValidateBulkDeposit_EmptyList_ShouldReturnFailure()
        {
            // Arrange
            var checks = new List<Check>();

            // Act
            var result = CheckBusinessRules.ValidateBulkDeposit(checks);

            // Assert
            Assert.False(result.IsValid);
            Assert.Contains("At least one check must be selected", result.GetErrorMessage());
        }

        [Fact]
        public void ValidateBulkDeposit_MixedTransactionTypes_ShouldReturnFailure()
        {
            // Arrange
            var check1 = new Check(Ulid.NewUlid(), "CHK001", DateTime.Today.AddDays(30), "Issuer", "Bearer", 1000m, 
                TransactionTypeEnumeration.TreasuryCheckIn.Value, DateTime.Today);
            var check2 = new Check(Ulid.NewUlid(), "CHK002", DateTime.Today.AddDays(30), "Issuer", "Bearer", 1000m, 
                TransactionTypeEnumeration.TreasuryCheckOut.Value, DateTime.Today);
            
            var checks = new List<Check> { check1, check2 };

            // Act
            var result = CheckBusinessRules.ValidateBulkDeposit(checks);

            // Assert
            Assert.False(result.IsValid);
            Assert.Contains("same transaction type", result.GetErrorMessage());
        }
    }
}
