using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.Extensions.DependencyInjection;
using SimpleBooks.Models.Domain.Treasury.CheckManagement;
using SimpleBooks.Models.Model.Treasury.BankManagement.CheckManagement;
using System.Net.Http.Json;
using System.Text.Json;
using Xunit;

namespace SimpleBooks.Tests.Integration.API
{
    /// <summary>
    /// Integration tests for Check Lifecycle API endpoints
    /// </summary>
    public class CheckLifecycleApiTests : IClassFixture<WebApplicationFactory<Program>>
    {
        private readonly WebApplicationFactory<Program> _factory;
        private readonly HttpClient _client;

        public CheckLifecycleApiTests(WebApplicationFactory<Program> factory)
        {
            _factory = factory;
            _client = _factory.CreateClient();
        }

        [Fact]
        public async Task DepositChecks_ValidRequest_ShouldReturnSuccess()
        {
            // Arrange
            var request = new
            {
                DepositId = Ulid.NewUlid(),
                CheckIds = new[] { Ulid.NewUlid(), Ulid.NewUlid() },
                DepositDate = DateTime.Today,
                BankId = Ulid.NewUlid(),
                BankAccountId = Ulid.NewUlid()
            };

            // Act
            var response = await _client.PostAsJsonAsync("/api/CheckLifecycle/deposit", request);

            // Assert
            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                var result = JsonSerializer.Deserialize<dynamic>(content);
                Assert.NotNull(result);
            }
            else
            {
                // Expected if checks don't exist in test database
                Assert.True(response.StatusCode == System.Net.HttpStatusCode.BadRequest);
            }
        }

        [Fact]
        public async Task GetCheck_ValidId_ShouldReturnCheckDetails()
        {
            // Arrange
            var checkId = Ulid.NewUlid();

            // Act
            var response = await _client.GetAsync($"/api/CheckLifecycle/{checkId}");

            // Assert
            // Expected to return NotFound for non-existent check
            Assert.True(response.StatusCode == System.Net.HttpStatusCode.NotFound || 
                       response.StatusCode == System.Net.HttpStatusCode.OK);
        }

        [Fact]
        public async Task ValidateDeposit_EmptyCheckIds_ShouldReturnValidationError()
        {
            // Arrange
            var request = new { CheckIds = new Ulid[0] };

            // Act
            var response = await _client.PostAsJsonAsync("/api/CheckLifecycle/validate-deposit", request);

            // Assert
            var content = await response.Content.ReadAsStringAsync();
            Assert.Contains("At least one check", content);
        }

        [Fact]
        public async Task RemoveCheckFromDeposit_ValidRequest_ShouldProcessCorrectly()
        {
            // Arrange
            var request = new
            {
                CheckId = Ulid.NewUlid(),
                DepositId = Ulid.NewUlid(),
                Reason = "Test removal"
            };

            // Act
            var response = await _client.PostAsJsonAsync("/api/CheckLifecycle/remove-from-deposit", request);

            // Assert
            // Expected to fail for non-existent check/deposit
            Assert.True(response.StatusCode == System.Net.HttpStatusCode.BadRequest);
        }

        [Fact]
        public async Task CollectCheck_ValidRequest_ShouldProcessCorrectly()
        {
            // Arrange
            var request = new
            {
                CollectionId = Ulid.NewUlid(),
                CheckId = Ulid.NewUlid(),
                DepositId = Ulid.NewUlid(),
                CollectionDate = DateTime.Today
            };

            // Act
            var response = await _client.PostAsJsonAsync("/api/CheckLifecycle/collect", request);

            // Assert
            // Expected to fail for non-existent check
            Assert.True(response.StatusCode == System.Net.HttpStatusCode.BadRequest);
        }

        [Fact]
        public async Task RejectCheck_ValidRequest_ShouldProcessCorrectly()
        {
            // Arrange
            var request = new
            {
                RejectId = Ulid.NewUlid(),
                CheckId = Ulid.NewUlid(),
                DepositId = Ulid.NewUlid(),
                RejectDate = DateTime.Today,
                Reason = "Insufficient funds"
            };

            // Act
            var response = await _client.PostAsJsonAsync("/api/CheckLifecycle/reject", request);

            // Assert
            // Expected to fail for non-existent check
            Assert.True(response.StatusCode == System.Net.HttpStatusCode.BadRequest);
        }

        [Theory]
        [InlineData("")]
        [InlineData("invalid-ulid")]
        [InlineData("00000000-0000-0000-0000-000000000000")]
        public async Task GetCheck_InvalidId_ShouldReturnBadRequest(string invalidId)
        {
            // Act
            var response = await _client.GetAsync($"/api/CheckLifecycle/{invalidId}");

            // Assert
            Assert.True(response.StatusCode == System.Net.HttpStatusCode.BadRequest ||
                       response.StatusCode == System.Net.HttpStatusCode.NotFound);
        }

        [Fact]
        public async Task GetChecksInDeposit_ValidDepositId_ShouldReturnChecks()
        {
            // Arrange
            var depositId = Ulid.NewUlid();

            // Act
            var response = await _client.GetAsync($"/api/CheckLifecycle/deposit/{depositId}/checks");

            // Assert
            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                var result = JsonSerializer.Deserialize<dynamic>(content);
                Assert.NotNull(result);
            }
            else
            {
                // Expected for non-existent deposit
                Assert.True(response.StatusCode == System.Net.HttpStatusCode.BadRequest);
            }
        }

        [Fact]
        public async Task DepositChecks_MissingRequiredFields_ShouldReturnBadRequest()
        {
            // Arrange
            var request = new
            {
                CheckIds = new[] { Ulid.NewUlid() }
                // Missing required fields
            };

            // Act
            var response = await _client.PostAsJsonAsync("/api/CheckLifecycle/deposit", request);

            // Assert
            Assert.Equal(System.Net.HttpStatusCode.BadRequest, response.StatusCode);
        }

        [Fact]
        public async Task RejectCheck_EmptyReason_ShouldReturnBadRequest()
        {
            // Arrange
            var request = new
            {
                RejectId = Ulid.NewUlid(),
                CheckId = Ulid.NewUlid(),
                DepositId = Ulid.NewUlid(),
                RejectDate = DateTime.Today,
                Reason = "" // Empty reason should fail validation
            };

            // Act
            var response = await _client.PostAsJsonAsync("/api/CheckLifecycle/reject", request);

            // Assert
            Assert.Equal(System.Net.HttpStatusCode.BadRequest, response.StatusCode);
        }
    }

    /// <summary>
    /// Integration tests for Check Deposit API endpoints
    /// </summary>
    public class CheckDepositApiTests : IClassFixture<WebApplicationFactory<Program>>
    {
        private readonly WebApplicationFactory<Program> _factory;
        private readonly HttpClient _client;

        public CheckDepositApiTests(WebApplicationFactory<Program> factory)
        {
            _factory = factory;
            _client = _factory.CreateClient();
        }

        [Fact]
        public async Task GetDepositSummary_ValidDepositId_ShouldReturnSummary()
        {
            // Arrange
            var depositId = Ulid.NewUlid();

            // Act
            var response = await _client.GetAsync($"/api/CheckDeposit/{depositId}/summary");

            // Assert
            // Expected to fail for non-existent deposit
            Assert.True(response.StatusCode == System.Net.HttpStatusCode.BadRequest ||
                       response.StatusCode == System.Net.HttpStatusCode.OK);
        }

        [Fact]
        public async Task GetRemovableChecks_ValidDepositId_ShouldReturnChecks()
        {
            // Arrange
            var depositId = Ulid.NewUlid();

            // Act
            var response = await _client.GetAsync($"/api/CheckDeposit/{depositId}/removable-checks");

            // Assert
            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                var result = JsonSerializer.Deserialize<dynamic>(content);
                Assert.NotNull(result);
            }
            else
            {
                // Expected for non-existent deposit
                Assert.True(response.StatusCode == System.Net.HttpStatusCode.BadRequest);
            }
        }

        [Fact]
        public async Task RemoveCheckFromDeposit_ValidRequest_ShouldProcessCorrectly()
        {
            // Arrange
            var depositId = Ulid.NewUlid();
            var request = new
            {
                CheckId = Ulid.NewUlid(),
                Reason = "Test removal reason"
            };

            // Act
            var response = await _client.PostAsJsonAsync($"/api/CheckDeposit/{depositId}/remove-check", request);

            // Assert
            // Expected to fail for non-existent deposit/check
            Assert.Equal(System.Net.HttpStatusCode.BadRequest, response.StatusCode);
        }
    }

    /// <summary>
    /// Performance tests for API endpoints
    /// </summary>
    public class CheckLifecyclePerformanceTests : IClassFixture<WebApplicationFactory<Program>>
    {
        private readonly WebApplicationFactory<Program> _factory;
        private readonly HttpClient _client;

        public CheckLifecyclePerformanceTests(WebApplicationFactory<Program> factory)
        {
            _factory = factory;
            _client = _factory.CreateClient();
        }

        [Fact]
        public async Task BulkDepositValidation_LargeNumberOfChecks_ShouldCompleteWithinTimeout()
        {
            // Arrange
            var checkIds = Enumerable.Range(0, 100).Select(_ => Ulid.NewUlid()).ToList();
            var request = new { CheckIds = checkIds };

            // Act
            var startTime = DateTime.UtcNow;
            var response = await _client.PostAsJsonAsync("/api/CheckLifecycle/validate-deposit", request);
            var endTime = DateTime.UtcNow;

            // Assert
            var duration = endTime - startTime;
            Assert.True(duration.TotalSeconds < 10, $"Validation took {duration.TotalSeconds} seconds, expected < 10");
        }

        [Fact]
        public async Task ConcurrentCheckStatusRequests_ShouldHandleLoad()
        {
            // Arrange
            var checkIds = Enumerable.Range(0, 10).Select(_ => Ulid.NewUlid()).ToList();
            var tasks = new List<Task<HttpResponseMessage>>();

            // Act
            foreach (var checkId in checkIds)
            {
                tasks.Add(_client.GetAsync($"/api/CheckLifecycle/{checkId}"));
            }

            var responses = await Task.WhenAll(tasks);

            // Assert
            Assert.All(responses, response => 
            {
                Assert.True(response.StatusCode == System.Net.HttpStatusCode.NotFound ||
                           response.StatusCode == System.Net.HttpStatusCode.OK ||
                           response.StatusCode == System.Net.HttpStatusCode.BadRequest);
            });
        }
    }
}
