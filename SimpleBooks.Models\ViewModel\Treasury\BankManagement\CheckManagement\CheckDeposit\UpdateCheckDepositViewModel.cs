﻿namespace SimpleBooks.Models.ViewModel.Treasury.BankManagement.CheckManagement.CheckDeposit
{
    public class UpdateCheckDepositViewModel : BaseUpdateViewModel, IEntityMapper<CheckDepositModel, UpdateCheckDepositViewModel>
    {
        [CustomRequired]
        [DisplayName("Deposit Date")]
        [Column(TypeName = "Date"), DataType(DataType.Date)]
        public DateTime DepositDate { get; set; }
        [DisplayName("Refrance Number")]
        public string? RefranceNumber { get; set; }
        [CustomRequired]
        [DisplayName("Bank")]
        public Ulid BankId { get; set; }
        [CustomRequired]
        [DisplayName("Bank Account")]
        public Ulid BankAccountId { get; set; }

        [CustomRequired]
        [DisplayName("Check Treasury Vouchers")]
        public virtual ICollection<Ulid> SelectedCheckTreasuryVouchers { get; set; } = new List<Ulid>();

        public UpdateCheckDepositViewModel ToDto(CheckDepositModel entity) => entity.ToUpdateDto();

        public CheckDepositModel ToEntity() => CheckDepositMapper.ToEntity(this);
    }
}
