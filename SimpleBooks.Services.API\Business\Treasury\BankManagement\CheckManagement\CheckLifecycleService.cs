﻿using SimpleBooks.Models.Domain.Treasury.CheckManagement;

namespace SimpleBooks.Services.API.Business.Treasury.BankManagement.CheckManagement
{
    //public class CheckLifecycleService : ICheckLifecycleService
    //{
    //    protected SessionIdentity SessionIdentity { get; private set; }
    //    protected readonly IAuthenticationValidationService _authenticationValidationService;
    //    protected HttpClient _httpClient;

    //    public CheckLifecycleService(
    //    IAuthenticationValidationService authenticationValidationService,
    //    IHttpClientFactory httpClientFactory)
    //    {
    //        _authenticationValidationService = authenticationValidationService;
    //        SessionIdentity = _authenticationValidationService.GetSessionIdentity();
    //        _httpClient = httpClientFactory.CreateClient("SimpleBooks") ?? new HttpClient();
    //    }

    //    [HttpRequestMethod(nameof(GetAllTreasuryVoucherChecks), HttpMethodEnum.GET)]
    //    public async Task<ServiceResult<IEnumerable<CheckTreasuryVoucherModel>>> GetAllTreasuryVoucherChecks(Ulid? checkStatusId)
    //    {
    //        var result = await _httpClient.SendRequest<ServiceResult<IEnumerable<CheckTreasuryVoucherModel>>>(this, new { checkStatusId }) ?? new List<CheckTreasuryVoucherModel>();
    //        return result;
    //    }

    //    [HttpRequestMethod(nameof(GetAllTreasuryVoucherChecks), HttpMethodEnum.GET)]
    //    public async Task<ServiceResult<List<Check>>> DepositChecksAsync(List<Ulid> checkIds, CheckDepositModel deposit)
    //    {
    //        var result = await _httpClient.SendRequest<ServiceResult<List<Check>>>(this, new { checkStatusId }) ?? new List<CheckTreasuryVoucherModel>();
    //        return result;
    //    }

    //    public async Task<ServiceResult<Check>> RemoveCheckFromDepositAsync(Ulid checkId, Ulid depositId, string reason = "")
    //    {
    //    }

    //    public async Task<ServiceResult<Check>> CollectCheckAsync(Ulid checkId, CheckCollectionModel collection)
    //    {
    //    }

    //    public async Task<ServiceResult<Check>> RejectCheckAsync(Ulid checkId, CheckRejectModel rejection)
    //    {
    //    }

    //    public async Task<ServiceResult<Check>> GetCheckAsync(Ulid checkId)
    //    {
    //    }

    //    public async Task<ServiceResult<List<Check>>> GetChecksInDepositAsync(Ulid depositId)
    //    {
    //    }
    //}
}
