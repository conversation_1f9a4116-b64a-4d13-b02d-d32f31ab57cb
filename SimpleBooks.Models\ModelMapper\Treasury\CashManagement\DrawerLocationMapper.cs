﻿namespace SimpleBooks.Models.ModelMapper.Treasury.CashManagement
{
    public static class DrawerLocationMapper
    {
        public static CreateDrawerLocationViewModel ToCreateDto(this DrawerLocationModel entity)
        {
            CreateDrawerLocationViewModel viewModel = new CreateDrawerLocationViewModel()
            {
                DrawerLocationNumber = entity.DrawerLocationNumber,
                DrawerLocationCurrency = entity.DrawerLocationCurrency,
            };
            return viewModel;
        }

        public static DrawerLocationModel ToEntity(this CreateDrawerLocationViewModel entity)
        {
            DrawerLocationModel model = new DrawerLocationModel()
            {
                DrawerLocationNumber = entity.DrawerLocationNumber,
                DrawerLocationCurrency = entity.DrawerLocationCurrency,
            };
            return model;
        }

        public static UpdateDrawerLocationViewModel ToUpdateDto(this DrawerLocationModel entity)
        {
            UpdateDrawerLocationViewModel viewModel = new UpdateDrawerLocationViewModel()
            {
                Id = entity.Id,
                DrawerLocationNumber = entity.DrawerLocationNumber,
                DrawerLocationCurrency = entity.DrawerLocationCurrency,
                DrawerId = entity.DrawerId,
            };
            return viewModel;
        }

        public static DrawerLocationModel ToEntity(this UpdateDrawerLocationViewModel entity)
        {
            DrawerLocationModel model = new DrawerLocationModel()
            {
                Id = entity.Id,
                DrawerLocationNumber = entity.DrawerLocationNumber,
                DrawerLocationCurrency = entity.DrawerLocationCurrency,
                DrawerId = entity.DrawerId,
            };
            return model;
        }
    }
}
