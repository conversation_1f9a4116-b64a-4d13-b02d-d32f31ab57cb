﻿namespace SimpleBooks.Models.ModelMapper.Treasury.BankManagement.CheckManagement
{
    public static class CheckCollectionMapper
    {
        public static CreateCheckCollectionViewModel ToCreateDto(this CheckCollectionModel entity)
        {
            CreateCheckCollectionViewModel viewModel = new CreateCheckCollectionViewModel()
            {
                CollectionDate = entity.CollectionDate,
                CheckDepositId = entity.CheckDepositId,
                CheckTreasuryVoucherId = entity.CheckTreasuryVoucherId,
                CheckStatusHistories = entity.CheckStatusHistories.Select(x => x.ToCreateDto()).ToList(),
            };
            return viewModel;
        }

        public static CheckCollectionModel ToEntity(this CreateCheckCollectionViewModel entity)
        {
            CheckCollectionModel model = new CheckCollectionModel()
            {
                CollectionDate = entity.CollectionDate,
                CheckDepositId = entity.CheckDepositId,
                CheckTreasuryVoucherId = entity.CheckTreasuryVoucherId,
                CheckStatusHistories = entity.CheckStatusHistories.Select(x => x.ToEntity()).ToList(),
            };
            return model;
        }

        public static UpdateCheckCollectionViewModel ToUpdateDto(this CheckCollectionModel entity)
        {
            UpdateCheckCollectionViewModel viewModel = new UpdateCheckCollectionViewModel()
            {
                Id = entity.Id,
                CollectionDate = entity.CollectionDate,
                CheckDepositId = entity.CheckDepositId,
                CheckTreasuryVoucherId = entity.CheckTreasuryVoucherId,
                CheckStatusHistories = entity.CheckStatusHistories.Select(x => x.ToUpdateDto()).ToList(),
            };
            return viewModel;
        }

        public static CheckCollectionModel ToEntity(this UpdateCheckCollectionViewModel entity)
        {
            CheckCollectionModel model = new CheckCollectionModel()
            {
                Id = entity.Id,
                CollectionDate = entity.CollectionDate,
                CheckDepositId = entity.CheckDepositId,
                CheckTreasuryVoucherId = entity.CheckTreasuryVoucherId,
                CheckStatusHistories = entity.CheckStatusHistories.Select(x => x.ToEntity()).ToList(),
            };
            return model;
        }
    }
}
