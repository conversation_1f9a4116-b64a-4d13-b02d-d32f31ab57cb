﻿namespace SimpleBooks.Repositories.EF.Factory.Config.Treasury.BankManagement.CheckManagement
{
    public class CheckStatusHistoryConfiguration : IEntityTypeConfiguration<CheckStatusHistoryModel>
    {
        public void Configure(EntityTypeBuilder<CheckStatusHistoryModel> builder)
        {
            builder.<PERSON><PERSON>ey(x => new { x.Id });

            builder.HasOne(d => d.CheckStatusFrom).WithMany(d => d.CheckStatusFromHistories)
                .HasForeignKey(d => d.CheckStatusFromId)
                .OnDelete(DeleteBehavior.ClientCascade);

            builder.HasOne(d => d.CheckStatusTo).WithMany(d => d.CheckStatusToHistories)
                .HasForeignKey(d => d.CheckStatusToId)
                .OnDelete(DeleteBehavior.ClientCascade);

            builder.HasOne(d => d.CheckTreasuryVoucher).WithMany(d => d.CheckStatusHistories)
                .HasForeignKey(d => d.CheckTreasuryVoucherId)
                .OnDelete(DeleteBehavior.ClientCascade);

            builder.HasOne(d => d.CheckDeposit).WithMany(d => d.CheckStatusHistories)
                .HasForeignKey(d => d.CheckDepositId)
                .OnDelete(DeleteBehavior.ClientCascade)
                .IsRequired(false);

            builder.HasOne(d => d.CheckCollection).WithMany(d => d.CheckStatusHistories)
                .HasForeignKey(d => d.CheckCollectionId)
                .OnDelete(DeleteBehavior.ClientCascade)
                .IsRequired(false);

            builder.HasOne(d => d.CheckReject).WithMany(d => d.CheckStatusHistories)
                .HasForeignKey(d => d.CheckRejectId)
                .OnDelete(DeleteBehavior.ClientCascade)
                .IsRequired(false);
        }
    }
}
