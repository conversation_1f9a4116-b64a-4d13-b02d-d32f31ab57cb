﻿namespace SimpleBooks.Models.ModelMapper.Treasury.BankManagement
{
    public static class BankMapper
    {
        public static CreateBankViewModel ToCreateDto(this BankModel entity)
        {
            CreateBankViewModel viewModel = new CreateBankViewModel()
            {
                BankName = entity.BankName,
                BankIdentifier = entity.BankIdentifier,
                BankAccounts = entity.BankAccounts.Select(x => x.ToCreateDto()).ToList(),
            };
            return viewModel;
        }

        public static BankModel ToEntity(this CreateBankViewModel entity)
        {
            BankModel model = new BankModel()
            {
                BankName = entity.BankName,
                BankIdentifier = entity.BankIdentifier,
                BankAccounts = entity.BankAccounts.Select(x => x.ToEntity()).ToList(),
            };
            return model;
        }

        public static UpdateBankViewModel ToUpdateDto(this BankModel entity)
        {
            UpdateBankViewModel viewModel = new UpdateBankViewModel()
            {
                Id = entity.Id,
                BankName = entity.BankName,
                BankIdentifier = entity.BankIdentifier,
                BankAccounts = entity.BankAccounts.Select(x => x.ToUpdateDto()).ToList(),
            };
            return viewModel;
        }

        public static BankModel ToEntity(this UpdateBankViewModel entity)
        {
            BankModel model = new BankModel()
            {
                Id = entity.Id,
                BankName = entity.BankName,
                BankIdentifier = entity.BankIdentifier,
                BankAccounts = entity.BankAccounts.Select(x => x.ToEntity()).ToList(),
            };
            return model;
        }
    }
}
