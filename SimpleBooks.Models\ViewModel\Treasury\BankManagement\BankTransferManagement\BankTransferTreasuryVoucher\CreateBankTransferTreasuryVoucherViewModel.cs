﻿namespace SimpleBooks.Models.ViewModel.Treasury.BankManagement.BankTransferManagement.BankTransferTreasuryVoucher
{
    public class CreateBankTransferTreasuryVoucherViewModel : CreateTreasuryVoucherViewModel, IEntityMapper<BankTransferTreasuryVoucherModel, CreateBankTransferTreasuryVoucherViewModel>
    {
        [CustomRequired]
        [DisplayName("Bank")]
        public Ulid BankId { get; set; }
        [CustomRequired]
        [DisplayName("Bank Account")]
        public Ulid BankAccountId { get; set; }

        public CreateBankTransferTreasuryVoucherViewModel ToDto(BankTransferTreasuryVoucherModel entity) => entity.ToCreateDto();

        public BankTransferTreasuryVoucherModel ToEntity() => BankTransferTreasuryVoucherMapper.ToEntity(this);
    }
}
