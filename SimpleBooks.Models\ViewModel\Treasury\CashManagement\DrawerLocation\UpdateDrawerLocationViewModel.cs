﻿namespace SimpleBooks.Models.ViewModel.Treasury.CashManagement.DrawerLocation
{
    public class UpdateDrawerLocationViewModel : BaseUpdateViewModel, IEntityMapper<DrawerLocationModel, UpdateDrawerLocationViewModel>
    {
        [CustomRequired]
        [DisplayN<PERSON>("Drawer Account Number")]
        public string DrawerLocationNumber { get; set; }
        [CustomRequired]
        [DisplayName("Drawer Account Currency")]
        public string DrawerLocationCurrency { get; set; }

        [DisplayName("Drawer")]
        public Ulid DrawerId { get; set; }

        public UpdateDrawerLocationViewModel ToDto(DrawerLocationModel entity) => entity.ToUpdateDto();

        public DrawerLocationModel ToEntity() => DrawerLocationMapper.ToEntity(this);
    }
}
