﻿namespace SimpleBooks.API.Controllers.Business.Treasury.BankManagement.CheckManagement
{
    public class CheckVaultController : BaseBusinessController<CheckVaultModel, CheckVaultModel, CreateCheckVaultViewModel, UpdateCheckVaultViewModel>
    {
        private readonly ICheckVaultService _checkVaultService;

        public CheckVaultController(ICheckVaultService checkVaultService) : base(checkVaultService)
        {
            _checkVaultService = checkVaultService;
        }
    }
}
