﻿namespace SimpleBooks.Models.ViewModel.Treasury.BankManagement.CheckManagement.CheckVaultLocation
{
    public class CreateCheckVaultLocationViewModel : BaseCreateViewModel, IEntityMapper<CheckVaultLocationModel, CreateCheckVaultLocationViewModel>
    {
        [CustomRequired]
        [DisplayName("Check Vault Account Number")]
        public string CheckVaultLocationNumber { get; set; }
        [CustomRequired]
        [DisplayName("Check Vault Account Currency")]
        public string CheckVaultLocationCurrency { get; set; }

        public CreateCheckVaultLocationViewModel ToDto(CheckVaultLocationModel entity) => entity.ToCreateDto();

        public CheckVaultLocationModel ToEntity() => CheckVaultLocationMapper.ToEntity(this);
    }
}
