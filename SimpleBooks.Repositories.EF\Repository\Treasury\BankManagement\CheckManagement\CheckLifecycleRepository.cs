using Microsoft.EntityFrameworkCore;
using SimpleBooks.Models.Model.Treasury.BankManagement.CheckManagement;
using SimpleBooks.Repositories.Core.IRepository.Treasury.BankManagement.CheckManagement;
using SimpleBooks.Repositories.EF.Factory.Contexts;

namespace SimpleBooks.Repositories.EF.Repository.Treasury.BankManagement.CheckManagement
{
    /// <summary>
    /// Implementation of check lifecycle repository with optimized queries
    /// </summary>
    internal class CheckLifecycleRepository : ICheckLifecycleRepository
    {
        private readonly ApplicationDBContext _context;

        public CheckLifecycleRepository(ApplicationDBContext context)
        {
            _context = context;
        }

        public async Task<List<CheckTreasuryVoucherModel>> GetChecksAvailableForDepositAsync(List<Ulid> checkIds)
        {
            return await _context.Set<CheckTreasuryVoucherModel>()
                .Include(c => c.CheckStatusHistories)
                .Where(c => checkIds.Contains(c.Id) && 
                           c.CheckStatusId == CheckStatusEnumeration.Received.Value &&
                           c.CheckDepositId == null)
                .ToListAsync();
        }

        public async Task<List<CheckTreasuryVoucherModel>> GetChecksInDepositAsync(Ulid depositId)
        {
            return await _context.Set<CheckTreasuryVoucherModel>()
                .Include(c => c.CheckStatusHistories)
                .Include(c => c.CheckCollections)
                .Include(c => c.CheckRejects)
                .Where(c => c.CheckDepositId == depositId)
                .OrderBy(c => c.CheckNumber)
                .ToListAsync();
        }

        public async Task<List<CheckTreasuryVoucherModel>> GetChecksRemovableFromDepositAsync(Ulid depositId)
        {
            return await _context.Set<CheckTreasuryVoucherModel>()
                .Include(c => c.CheckStatusHistories)
                .Where(c => c.CheckDepositId == depositId && 
                           c.CheckStatusId == CheckStatusEnumeration.Deposited.Value)
                .ToListAsync();
        }

        public async Task<List<CheckTreasuryVoucherModel>> GetChecksAvailableForCollectionAsync(Ulid depositId)
        {
            return await _context.Set<CheckTreasuryVoucherModel>()
                .Include(c => c.CheckStatusHistories)
                .Where(c => c.CheckDepositId == depositId && 
                           c.CheckStatusId == CheckStatusEnumeration.Deposited.Value &&
                           !c.CheckCollections.Any() &&
                           !c.CheckRejects.Any())
                .ToListAsync();
        }

        public async Task<List<CheckTreasuryVoucherModel>> GetChecksAvailableForRejectionAsync(Ulid depositId)
        {
            return await _context.Set<CheckTreasuryVoucherModel>()
                .Include(c => c.CheckStatusHistories)
                .Where(c => c.CheckDepositId == depositId && 
                           c.CheckStatusId == CheckStatusEnumeration.Deposited.Value &&
                           !c.CheckCollections.Any() &&
                           !c.CheckRejects.Any())
                .ToListAsync();
        }

        public async Task<CheckTreasuryVoucherModel?> GetCheckWithAuditTrailAsync(Ulid checkId)
        {
            return await _context.Set<CheckTreasuryVoucherModel>()
                .Include(c => c.CheckStatusHistories)
                    .ThenInclude(h => h.CheckStatusFrom)
                .Include(c => c.CheckStatusHistories)
                    .ThenInclude(h => h.CheckStatusTo)
                .Include(c => c.CheckDeposit)
                .Include(c => c.CheckCollections)
                .Include(c => c.CheckRejects)
                .FirstOrDefaultAsync(c => c.Id == checkId);
        }

        public async Task<T> ExecuteInTransactionAsync<T>(Func<Task<T>> operation)
        {
            using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                var result = await operation();
                await transaction.CommitAsync();
                return result;
            }
            catch
            {
                await transaction.RollbackAsync();
                throw;
            }
        }

        public async Task BulkUpdateCheckStatusesAsync(List<Ulid> checkIds, Ulid newStatusId, Ulid? depositId = null)
        {
            var checks = await _context.Set<CheckTreasuryVoucherModel>()
                .Where(c => checkIds.Contains(c.Id))
                .ToListAsync();

            foreach (var check in checks)
            {
                check.CheckStatusId = newStatusId;
                if (depositId.HasValue)
                    check.CheckDepositId = depositId;
            }

            await _context.SaveChangesAsync();
        }

        public async Task RemoveDepositStatusHistoryAsync(Ulid checkId, Ulid depositId)
        {
            var historyEntries = await _context.Set<CheckStatusHistoryModel>()
                .Where(h => h.CheckTreasuryVoucherId == checkId && 
                           h.CheckDepositId == depositId &&
                           h.CheckStatusToId == CheckStatusEnumeration.Deposited.Value)
                .ToListAsync();

            _context.Set<CheckStatusHistoryModel>().RemoveRange(historyEntries);
            await _context.SaveChangesAsync();
        }

        public async Task<(List<CheckTreasuryVoucherModel> Checks, int TotalCount)> GetChecksByStatusAsync(
            Ulid statusId, 
            int pageNumber = 1, 
            int pageSize = 50)
        {
            var query = _context.Set<CheckTreasuryVoucherModel>()
                .Include(c => c.CheckStatusHistories)
                .Where(c => c.CheckStatusId == statusId);

            var totalCount = await query.CountAsync();
            
            var checks = await query
                .OrderBy(c => c.TransactionDate)
                .ThenBy(c => c.CheckNumber)
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            return (checks, totalCount);
        }

        public async Task<DepositSummary> GetDepositSummaryAsync(Ulid depositId)
        {
            var checks = await _context.Set<CheckTreasuryVoucherModel>()
                .Where(c => c.CheckDepositId == depositId)
                .ToListAsync();

            var summary = new DepositSummary
            {
                DepositId = depositId,
                TotalChecks = checks.Count,
                TotalAmount = checks.Sum(c => c.Amount)
            };

            summary.DepositedChecks = checks.Count(c => c.CheckStatusId == CheckStatusEnumeration.Deposited.Value);
            summary.CollectedChecks = checks.Count(c => c.CheckStatusId == CheckStatusEnumeration.Cleared.Value);
            summary.RejectedChecks = checks.Count(c => c.CheckStatusId == CheckStatusEnumeration.Rejected.Value);

            summary.CollectedAmount = checks
                .Where(c => c.CheckStatusId == CheckStatusEnumeration.Cleared.Value)
                .Sum(c => c.Amount);

            summary.RejectedAmount = checks
                .Where(c => c.CheckStatusId == CheckStatusEnumeration.Rejected.Value)
                .Sum(c => c.Amount);

            return summary;
        }
    }
}
