﻿namespace SimpleBooks.Models.ModelMapper.Treasury.CashManagement
{
    public static class DrawerMapper
    {
        public static CreateDrawerViewModel ToCreateDto(this DrawerModel entity)
        {
            CreateDrawerViewModel viewModel = new CreateDrawerViewModel()
            {
                DrawerName = entity.DrawerName,
                DrawerLocations = entity.DrawerLocations.Select(x => x.ToCreateDto()).ToList(),
            };
            return viewModel;
        }

        public static DrawerModel ToEntity(this CreateDrawerViewModel entity)
        {
            DrawerModel model = new DrawerModel()
            {
                DrawerName = entity.DrawerName,
                DrawerLocations = entity.DrawerLocations.Select(x => x.ToEntity()).ToList(),
            };
            return model;
        }

        public static UpdateDrawerViewModel ToUpdateDto(this DrawerModel entity)
        {
            UpdateDrawerViewModel viewModel = new UpdateDrawerViewModel()
            {
                Id = entity.Id,
                DrawerName = entity.DrawerName,
                DrawerLocations = entity.DrawerLocations.Select(x => x.ToUpdateDto()).ToList(),
            };
            return viewModel;
        }

        public static DrawerModel ToEntity(this UpdateDrawerViewModel entity)
        {
            DrawerModel model = new DrawerModel()
            {
                Id = entity.Id,
                DrawerName = entity.DrawerName,
                DrawerLocations = entity.DrawerLocations.Select(x => x.ToEntity()).ToList(),
            };
            return model;
        }
    }
}
