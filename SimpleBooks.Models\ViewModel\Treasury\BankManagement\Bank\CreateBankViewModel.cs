﻿namespace SimpleBooks.Models.ViewModel.Treasury.BankManagement.Bank
{
    public class CreateBankViewModel : BaseCreateViewModel, IEntityMapper<BankModel, CreateBankViewModel>
    {
        [CustomRequired]
        [DisplayN<PERSON>("Bank Name")]
        public string BankName { get; set; }
        [Display<PERSON><PERSON>("Bank Identifier Code")]
        public string? BankIdentifier { get; set; }

        [CustomRequired]
        [DisplayName("Bank Accounts")]
        public IList<CreateBankAccountViewModel> BankAccounts { get; set; } = new List<CreateBankAccountViewModel>();

        public CreateBankViewModel ToDto(BankModel entity) => entity.ToCreateDto();

        public BankModel ToEntity() => BankMapper.ToEntity(this);
    }
}
