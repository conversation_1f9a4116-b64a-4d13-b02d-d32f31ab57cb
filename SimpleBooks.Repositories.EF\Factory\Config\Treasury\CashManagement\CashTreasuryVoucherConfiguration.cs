﻿namespace SimpleBooks.Repositories.EF.Factory.Config.Treasury.CashManagement
{
    public class CashTreasuryVoucherConfiguration : IEntityTypeConfiguration<CashTreasuryVoucherModel>
    {
        public void Configure(EntityTypeBuilder<CashTreasuryVoucherModel> builder)
        {
            builder.HasKey(x => new { x.Id });

            builder.HasOne(d => d.BeneficiaryType).WithMany(d => d.CashTreasuryVouchers)
                .HasForeignKey(d => d.BeneficiaryTypeId)
                .OnDelete(DeleteBehavior.ClientCascade);

            builder.HasOne(d => d.Vendor).WithMany(d => d.CashTreasuryVouchers)
                .HasForeignKey(d => d.VendorId)
                .OnDelete(DeleteBehavior.ClientCascade)
                .IsRequired(false);

            builder.HasOne(d => d.Customer).WithMany(d => d.CashTreasuryVouchers)
                .HasForeignKey(d => d.CustomerId)
                .OnDelete(DeleteBehavior.ClientCascade)
                .IsRequired(false);

            builder.HasOne(d => d.Employee).WithMany(d => d.CashTreasuryVouchers)
                .HasForeignKey(d => d.EmployeeId)
                .OnDelete(DeleteBehavior.ClientCascade)
                .IsRequired(false);

            builder.HasOne(d => d.TransactionType).WithMany(p => p.CashTreasuryVouchers)
                .HasForeignKey(d => d.TransactionTypeId)
                .OnDelete(DeleteBehavior.ClientCascade);

            builder.HasOne(d => d.Drawer).WithMany(p => p.CashTreasuryVouchers)
                .HasForeignKey(d => d.DrawerId)
                .OnDelete(DeleteBehavior.ClientCascade);

            builder.HasOne(d => d.DrawerLocation).WithMany(p => p.CashTreasuryVouchers)
                .HasForeignKey(d => d.DrawerLocationId)
                .OnDelete(DeleteBehavior.ClientCascade);
        }
    }
}
