﻿namespace SimpleBooks.Models.ViewModel.Treasury.CashManagement.Drawer
{
    public class CreateDrawerViewModel : BaseCreateViewModel, IEntityMapper<DrawerModel, CreateDrawerViewModel>
    {
        [CustomRequired]
        [DisplayName("Drawer Name")]
        public string DrawerName { get; set; }

        [CustomRequired]
        [DisplayName("Drawer Locations")]
        public IList<CreateDrawerLocationViewModel> DrawerLocations { get; set; } = new List<CreateDrawerLocationViewModel>();

        public CreateDrawerViewModel ToDto(DrawerModel entity) => entity.ToCreateDto();

        public DrawerModel ToEntity() => DrawerMapper.ToEntity(this);
    }
}
