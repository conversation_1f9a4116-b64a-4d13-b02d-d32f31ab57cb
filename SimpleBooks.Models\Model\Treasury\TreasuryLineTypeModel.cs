﻿namespace SimpleBooks.Models.Model.Treasury
{
    [Table("TreasuryLineType")]
    [TypeDescriptionProvider(typeof(BaseTypeDescriptionProvider<TreasuryLineTypeModel>))]
    public class TreasuryLineTypeModel : BaseWithoutTrackingModel
    {
        [CustomRequired]
        [DisplayName("Treasury Line Type Name")]
        public string TreasuryLineTypeName { get; set; }

        public virtual ICollection<InventoryModel> Inventories { get; set; } = new List<InventoryModel>();
        [DisplayName("Cash Treasury Vouchers")]
        public virtual ICollection<CashTreasuryVoucherModel> CashTreasuryVouchers { get; set; } = new List<CashTreasuryVoucherModel>();
        [DisplayName("Check Treasury Vouchers")]
        public virtual ICollection<CheckTreasuryVoucherModel> CheckTreasuryVouchers { get; set; } = new List<CheckTreasuryVoucherModel>();
        [DisplayName("Bank Transfer Treasury Vouchers")]
        public virtual ICollection<BankTransferTreasuryVoucherModel> BankTransferTreasuryVouchers { get; set; } = new List<BankTransferTreasuryVoucherModel>();
    }
}
