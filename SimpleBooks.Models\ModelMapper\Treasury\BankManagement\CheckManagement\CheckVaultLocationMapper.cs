﻿namespace SimpleBooks.Models.ModelMapper.Treasury.BankManagement.CheckManagement
{
    public static class CheckVaultLocationMapper
    {
        public static CreateCheckVaultLocationViewModel ToCreateDto(this CheckVaultLocationModel entity)
        {
            CreateCheckVaultLocationViewModel viewModel = new CreateCheckVaultLocationViewModel()
            {
                CheckVaultLocationNumber = entity.CheckVaultLocationNumber,
                CheckVaultLocationCurrency = entity.CheckVaultLocationCurrency,
            };
            return viewModel;
        }

        public static CheckVaultLocationModel ToEntity(this CreateCheckVaultLocationViewModel entity)
        {
            CheckVaultLocationModel model = new CheckVaultLocationModel()
            {
                CheckVaultLocationNumber = entity.CheckVaultLocationNumber,
                CheckVaultLocationCurrency = entity.CheckVaultLocationCurrency,
            };
            return model;
        }

        public static UpdateCheckVaultLocationViewModel ToUpdateDto(this CheckVaultLocationModel entity)
        {
            UpdateCheckVaultLocationViewModel viewModel = new UpdateCheckVaultLocationViewModel()
            {
                Id = entity.Id,
                CheckVaultLocationNumber = entity.CheckVaultLocationNumber,
                CheckVaultLocationCurrency = entity.CheckVaultLocationCurrency,
                CheckVaultId = entity.CheckVaultId,
            };
            return viewModel;
        }

        public static CheckVaultLocationModel ToEntity(this UpdateCheckVaultLocationViewModel entity)
        {
            CheckVaultLocationModel model = new CheckVaultLocationModel()
            {
                Id = entity.Id,
                CheckVaultLocationNumber = entity.CheckVaultLocationNumber,
                CheckVaultLocationCurrency = entity.CheckVaultLocationCurrency,
                CheckVaultId = entity.CheckVaultId,
            };
            return model;
        }
    }
}
