﻿namespace SimpleBooks.Repositories.EF.Factory.Config.Treasury.BankManagement.CheckManagement
{
    public class CheckVaultLocationConfiguration : IEntityTypeConfiguration<CheckVaultLocationModel>
    {
        public void Configure(EntityTypeBuilder<CheckVaultLocationModel> builder)
        {
            builder.HasKey(x => new { x.Id });

            builder.HasOne(d => d.CheckVault).WithMany(p => p.CheckVaultLocations)
                .HasForeignKey(d => d.CheckVaultId)
                .OnDelete(DeleteBehavior.ClientCascade);
        }
    }
}
