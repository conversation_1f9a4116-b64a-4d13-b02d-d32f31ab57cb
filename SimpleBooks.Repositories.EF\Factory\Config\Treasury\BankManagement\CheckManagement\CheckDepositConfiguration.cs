﻿namespace SimpleBooks.Repositories.EF.Factory.Config.Treasury.BankManagement.BankTransferManagement
{
    public class CheckDepositConfiguration : IEntityTypeConfiguration<CheckDepositModel>
    {
        public void Configure(EntityTypeBuilder<CheckDepositModel> builder)
        {
            builder.<PERSON>Key(x => new { x.Id });

            builder.HasOne(d => d.Bank).WithMany(p => p.CheckDeposits)
                .HasForeignKey(d => d.BankId)
                .OnDelete(DeleteBehavior.ClientCascade);

            builder.HasOne(d => d.BankAccount).WithMany(p => p.CheckDeposits)
                .HasForeignKey(d => d.BankAccountId)
                .OnDelete(DeleteBehavior.ClientCascade);
        }
    }
}
