using SimpleBooks.Models.Model.Treasury.BankManagement.CheckManagement;

namespace SimpleBooks.Repositories.Core.IRepository.Treasury.BankManagement.CheckManagement
{
    /// <summary>
    /// Repository interface for check lifecycle operations with optimized queries
    /// </summary>
    public interface ICheckLifecycleRepository
    {
        /// <summary>
        /// Get checks available for deposit (in Received status)
        /// </summary>
        Task<List<CheckTreasuryVoucherModel>> GetChecksAvailableForDepositAsync(List<Ulid> checkIds);

        /// <summary>
        /// Get checks in a specific deposit
        /// </summary>
        Task<List<CheckTreasuryVoucherModel>> GetChecksInDepositAsync(Ulid depositId);

        /// <summary>
        /// Get checks that can be removed from deposit (in Deposited status only)
        /// </summary>
        Task<List<CheckTreasuryVoucherModel>> GetChecksRemovableFromDepositAsync(Ulid depositId);

        /// <summary>
        /// Get checks available for collection (in Deposited status)
        /// </summary>
        Task<List<CheckTreasuryVoucherModel>> GetChecksAvailableForCollectionAsync(Ulid depositId);

        /// <summary>
        /// Get checks available for rejection (in Deposited status)
        /// </summary>
        Task<List<CheckTreasuryVoucherModel>> GetChecksAvailableForRejectionAsync(Ulid depositId);

        /// <summary>
        /// Get check with full audit trail
        /// </summary>
        Task<CheckTreasuryVoucherModel?> GetCheckWithAuditTrailAsync(Ulid checkId);

        /// <summary>
        /// Execute check lifecycle operation within transaction
        /// </summary>
        Task<T> ExecuteInTransactionAsync<T>(Func<Task<T>> operation);

        /// <summary>
        /// Bulk update check statuses
        /// </summary>
        Task BulkUpdateCheckStatusesAsync(List<Ulid> checkIds, Ulid newStatusId, Ulid? depositId = null);

        /// <summary>
        /// Remove status history entries for deposit rollback
        /// </summary>
        Task RemoveDepositStatusHistoryAsync(Ulid checkId, Ulid depositId);

        /// <summary>
        /// Get checks by status with pagination
        /// </summary>
        Task<(List<CheckTreasuryVoucherModel> Checks, int TotalCount)> GetChecksByStatusAsync(
            Ulid statusId, 
            int pageNumber = 1, 
            int pageSize = 50);

        /// <summary>
        /// Get deposit summary with check counts by status
        /// </summary>
        Task<DepositSummary> GetDepositSummaryAsync(Ulid depositId);
    }

    /// <summary>
    /// Summary information for a deposit
    /// </summary>
    public class DepositSummary
    {
        public Ulid DepositId { get; set; }
        public int TotalChecks { get; set; }
        public int DepositedChecks { get; set; }
        public int CollectedChecks { get; set; }
        public int RejectedChecks { get; set; }
        public decimal TotalAmount { get; set; }
        public decimal CollectedAmount { get; set; }
        public decimal RejectedAmount { get; set; }
    }
}
