﻿namespace SimpleBooks.Models.ModelMapper.Treasury
{
    public static class TreasuryLineMapper
    {
        public static CreateTreasuryLineViewModel ToCreateDto(this TreasuryLineModel entity)
        {
            CreateTreasuryLineViewModel viewModel = new CreateTreasuryLineViewModel()
            {
                Amount = entity.Amount,
                TreasuryLineTypeId = entity.TreasuryLineTypeId,
                AccountId = GetAccountId(entity),
            };
            return viewModel;
        }

        public static TreasuryLineModel ToEntity(this CreateTreasuryLineViewModel entity)
        {
            TreasuryLineModel model = new TreasuryLineModel()
            {
                Amount = entity.Amount,
                TreasuryLineTypeId = entity.TreasuryLineTypeId,
                ExpensesId = entity.TreasuryLineTypeId == TreasuryLineTypeEnumeration.Expenses.Value ? entity.AccountId : null,
                BillId = entity.TreasuryLineTypeId == TreasuryLineTypeEnumeration.Bill.Value ? entity.AccountId : null,
                BillReturnId = entity.TreasuryLineTypeId == TreasuryLineTypeEnumeration.BillReturn.Value ? entity.AccountId : null,
                InvoiceId = entity.TreasuryLineTypeId == TreasuryLineTypeEnumeration.Invoice.Value ? entity.AccountId : null,
                InvoiceReturnId = entity.TreasuryLineTypeId == TreasuryLineTypeEnumeration.InvoiceReturn.Value ? entity.AccountId : null,
            };
            return model;
        }

        public static UpdateTreasuryLineViewModel ToUpdateDto(this TreasuryLineModel entity)
        {
            UpdateTreasuryLineViewModel viewModel = new UpdateTreasuryLineViewModel()
            {
                Id = entity.Id,
                Amount = entity.Amount,
                TreasuryVoucherId = entity.CheckTreasuryVoucherId ?? Ulid.Empty,
                TreasuryLineTypeId = entity.TreasuryLineTypeId,
                AccountId = GetAccountId(entity),
            };
            return viewModel;
        }

        public static TreasuryLineModel ToEntity(this UpdateTreasuryLineViewModel entity)
        {
            TreasuryLineModel model = new TreasuryLineModel()
            {
                Id = entity.Id,
                Amount = entity.Amount,
                CheckTreasuryVoucherId = entity.TreasuryVoucherId,
                TreasuryLineTypeId = entity.TreasuryLineTypeId,
                ExpensesId = entity.TreasuryLineTypeId == TreasuryLineTypeEnumeration.Expenses.Value ? entity.AccountId : null,
                BillId = entity.TreasuryLineTypeId == TreasuryLineTypeEnumeration.Bill.Value ? entity.AccountId : null,
                BillReturnId = entity.TreasuryLineTypeId == TreasuryLineTypeEnumeration.BillReturn.Value ? entity.AccountId : null,
                InvoiceId = entity.TreasuryLineTypeId == TreasuryLineTypeEnumeration.Invoice.Value ? entity.AccountId : null,
                InvoiceReturnId = entity.TreasuryLineTypeId == TreasuryLineTypeEnumeration.InvoiceReturn.Value ? entity.AccountId : null,
            };
            return model;
        }

        private static Ulid GetAccountId(TreasuryLineModel entity)
        {
            if (entity.ExpensesId.HasValue)
                return entity.ExpensesId.Value;
            else if (entity.BillId.HasValue)
                return entity.BillId.Value;
            else if (entity.BillReturnId.HasValue)
                return entity.BillReturnId.Value;
            else if (entity.InvoiceId.HasValue)
                return entity.InvoiceId.Value;
            else if (entity.InvoiceReturnId.HasValue)
                return entity.InvoiceReturnId.Value;
            throw new InvalidOperationException("No account ID found.");
        }
    }
}
