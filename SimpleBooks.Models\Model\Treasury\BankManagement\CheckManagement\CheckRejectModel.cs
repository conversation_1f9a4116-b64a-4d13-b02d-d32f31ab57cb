﻿namespace SimpleBooks.Models.Model.Treasury.BankManagement.CheckManagement
{
    [Table("CheckReject")]
    [TypeDescriptionProvider(typeof(BaseTypeDescriptionProvider<CheckRejectModel>))]
    public partial class CheckRejectModel : BaseModel
    {
        [CustomRequired]
        [DisplayName("Reject Date")]
        [Column(TypeName = "Date"), DataType(DataType.Date)]
        public DateTime RejectDate { get; set; }
        [DisplayName("Refrance Number")]
        public string? RefranceNumber { get; set; }

        [CustomRequired]
        [DisplayName("Check Deposit")]
        public Ulid CheckDepositId { get; set; }
        public virtual CheckDepositModel? CheckDeposit { get; set; }

        [CustomRequired]
        [DisplayName("Check Treasury Voucher")]
        public Ulid CheckTreasuryVoucherId { get; set; }
        public virtual CheckTreasuryVoucherModel? CheckTreasuryVoucher { get; set; }

        [CustomRequired]
        [DisplayName("Check Status Histories")]
        public virtual ICollection<CheckStatusHistoryModel> CheckStatusHistories { get; set; } = new List<CheckStatusHistoryModel>();
    }
}
