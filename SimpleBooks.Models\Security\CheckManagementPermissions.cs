namespace SimpleBooks.Models.Security
{
    /// <summary>
    /// Defines permissions for check management operations
    /// </summary>
    public static class CheckManagementPermissions
    {
        // Check Treasury Voucher Permissions
        public const string ViewChecks = "Permissions.CheckManagement.ViewChecks";
        public const string CreateChecks = "Permissions.CheckManagement.CreateChecks";
        public const string EditChecks = "Permissions.CheckManagement.EditChecks";
        public const string DeleteChecks = "Permissions.CheckManagement.DeleteChecks";

        // Check Deposit Permissions
        public const string ViewDeposits = "Permissions.CheckManagement.ViewDeposits";
        public const string CreateDeposits = "Permissions.CheckManagement.CreateDeposits";
        public const string EditDeposits = "Permissions.CheckManagement.EditDeposits";
        public const string DeleteDeposits = "Permissions.CheckManagement.DeleteDeposits";

        // Check Lifecycle Permissions
        public const string DepositChecks = "Permissions.CheckManagement.DepositChecks";
        public const string RemoveFromDeposit = "Permissions.CheckManagement.RemoveFromDeposit";
        public const string CollectChecks = "Permissions.CheckManagement.CollectChecks";
        public const string RejectChecks = "Permissions.CheckManagement.RejectChecks";

        // Advanced Permissions
        public const string ViewAuditTrail = "Permissions.CheckManagement.ViewAuditTrail";
        public const string ViewReports = "Permissions.CheckManagement.ViewReports";
        public const string ExportData = "Permissions.CheckManagement.ExportData";
        public const string ManageCheckStatuses = "Permissions.CheckManagement.ManageCheckStatuses";

        // Administrative Permissions
        public const string AdministerCheckManagement = "Permissions.CheckManagement.Administer";
        public const string ViewAllUserChecks = "Permissions.CheckManagement.ViewAllUserChecks";
        public const string OverrideBusinessRules = "Permissions.CheckManagement.OverrideBusinessRules";

        /// <summary>
        /// Get all check management permissions
        /// </summary>
        public static IEnumerable<string> GetAllPermissions()
        {
            return new[]
            {
                ViewChecks, CreateChecks, EditChecks, DeleteChecks,
                ViewDeposits, CreateDeposits, EditDeposits, DeleteDeposits,
                DepositChecks, RemoveFromDeposit, CollectChecks, RejectChecks,
                ViewAuditTrail, ViewReports, ExportData, ManageCheckStatuses,
                AdministerCheckManagement, ViewAllUserChecks, OverrideBusinessRules
            };
        }

        /// <summary>
        /// Get basic user permissions (read-only access)
        /// </summary>
        public static IEnumerable<string> GetBasicUserPermissions()
        {
            return new[]
            {
                ViewChecks, ViewDeposits
            };
        }

        /// <summary>
        /// Get check operator permissions (can perform check operations)
        /// </summary>
        public static IEnumerable<string> GetCheckOperatorPermissions()
        {
            return new[]
            {
                ViewChecks, CreateChecks, EditChecks,
                ViewDeposits, CreateDeposits,
                DepositChecks, CollectChecks, RejectChecks,
                ViewAuditTrail
            };
        }

        /// <summary>
        /// Get supervisor permissions (can manage deposits and override some rules)
        /// </summary>
        public static IEnumerable<string> GetSupervisorPermissions()
        {
            return GetCheckOperatorPermissions().Concat(new[]
            {
                EditDeposits, RemoveFromDeposit,
                ViewReports, ExportData,
                ManageCheckStatuses
            });
        }

        /// <summary>
        /// Get administrator permissions (full access)
        /// </summary>
        public static IEnumerable<string> GetAdministratorPermissions()
        {
            return GetAllPermissions();
        }
    }

    /// <summary>
    /// Defines roles for check management
    /// </summary>
    public static class CheckManagementRoles
    {
        public const string CheckViewer = "CheckViewer";
        public const string CheckOperator = "CheckOperator";
        public const string CheckSupervisor = "CheckSupervisor";
        public const string CheckAdministrator = "CheckAdministrator";
        public const string SystemAdministrator = "SystemAdministrator";

        /// <summary>
        /// Get all check management roles
        /// </summary>
        public static IEnumerable<string> GetAllRoles()
        {
            return new[]
            {
                CheckViewer, CheckOperator, CheckSupervisor, 
                CheckAdministrator, SystemAdministrator
            };
        }
    }

    /// <summary>
    /// Security policies for check management operations
    /// </summary>
    public static class CheckManagementPolicies
    {
        public const string CanViewChecks = "CanViewChecks";
        public const string CanManageChecks = "CanManageChecks";
        public const string CanDepositChecks = "CanDepositChecks";
        public const string CanCollectChecks = "CanCollectChecks";
        public const string CanRejectChecks = "CanRejectChecks";
        public const string CanRemoveFromDeposit = "CanRemoveFromDeposit";
        public const string CanViewAuditTrail = "CanViewAuditTrail";
        public const string CanViewReports = "CanViewReports";
        public const string CanAdministerChecks = "CanAdministerChecks";

        /// <summary>
        /// Get all check management policies
        /// </summary>
        public static IEnumerable<string> GetAllPolicies()
        {
            return new[]
            {
                CanViewChecks, CanManageChecks, CanDepositChecks,
                CanCollectChecks, CanRejectChecks, CanRemoveFromDeposit,
                CanViewAuditTrail, CanViewReports, CanAdministerChecks
            };
        }
    }
}
