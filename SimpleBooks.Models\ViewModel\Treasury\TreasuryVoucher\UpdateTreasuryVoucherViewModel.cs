﻿namespace SimpleBooks.Models.ViewModel.Treasury.TreasuryVoucher
{
    public class UpdateTreasuryVoucherViewModel : BaseUpdateViewModel
    {
        [CustomRequired]
        [DisplayName("Transaction Date")]
        [Column(TypeName = "Date"), DataType(DataType.Date)]
        public DateTime TransactionDate { get; set; }
        [CustomRequired]
        [DisplayName("Transaction Serial")]
        public int VoucherSerial { get; set; }
        [CustomRequired]
        [DisplayName("TVID")]
        public int TVID { get; set; }
        [CustomRequired]
        [DisplayName("Amount")]
        public decimal Amount { get; set; }
        [CustomRequired]
        [DisplayName("Note")]
        public string Note { get; set; }
        [DisplayName("Refrance Number")]
        public string? RefranceNumber { get; set; }

        [CustomRequired]
        [DisplayName("Transaction Type")]
        public Ulid TransactionTypeId { get; set; }
        [CustomRequired]
        [DisplayName("Beneficiary Type")]
        public Ulid BeneficiaryTypeId { get; set; }
        [CustomRequired]
        [DisplayName("Beneficiary")]
        public Ulid BeneficiaryId { get; set; }

        [CustomRequired]
        [DisplayName("Treasury Lines")]
        public IList<UpdateTreasuryLineViewModel> TreasuryLines { get; set; } = new List<UpdateTreasuryLineViewModel>();
    }
}
