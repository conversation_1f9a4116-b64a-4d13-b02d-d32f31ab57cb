﻿namespace SimpleBooks.Models.ViewModel.Treasury.BankManagement.CheckManagement.CheckReject
{
    public class UpdateCheckRejectViewModel : BaseUpdateViewModel, IEntityMapper<CheckRejectModel, UpdateCheckRejectViewModel>
    {
        [CustomRequired]
        [DisplayName("Reject Date")]
        [Column(TypeName = "Date"), DataType(DataType.Date)]
        public DateTime RejectDate { get; set; }
        [DisplayName("Refrance Number")]
        public string? RefranceNumber { get; set; }

        [CustomRequired]
        [DisplayName("Check Deposit")]
        public Ulid CheckDepositId { get; set; }
        [CustomRequired]
        [DisplayName("Check Treasury Voucher")]
        public Ulid CheckTreasuryVoucherId { get; set; }

        [CustomRequired]
        [DisplayName("Check Status Histories")]
        public virtual ICollection<UpdateCheckStatusHistoryViewModel> CheckStatusHistories { get; set; } = new List<UpdateCheckStatusHistoryViewModel>();

        public UpdateCheckRejectViewModel ToDto(CheckRejectModel entity) => entity.ToUpdateDto();

        public CheckRejectModel ToEntity() => CheckRejectMapper.ToEntity(this);
    }
}
