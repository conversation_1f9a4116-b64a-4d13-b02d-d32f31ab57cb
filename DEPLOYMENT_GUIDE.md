# Check Management System - Deployment Guide

## Overview
This guide provides step-by-step instructions for deploying the refactored check management system with all new features including domain-driven design, enhanced security, and comprehensive reporting.

## Prerequisites

### System Requirements
- .NET 8.0 or later
- MySQL 8.0 or later
- IIS 10.0 or later (for Windows deployment)
- 4GB RAM minimum (8GB recommended)
- 10GB free disk space

### Development Tools
- Visual Studio 2022 or Visual Studio Code
- MySQL Workbench or similar database management tool
- Git for version control

## Database Setup

### 1. Database Migration
```sql
-- Run the following scripts in order:

-- 1. Update existing tables with new indexes
ALTER TABLE CheckTreasuryVoucher 
ADD INDEX IX_CheckTreasuryVoucher_Status (CheckStatusId),
ADD INDEX IX_CheckTreasuryVoucher_Deposit (CheckDepositId),
ADD INDEX IX_CheckTreasuryVoucher_Status_Deposit (CheckStatusId, CheckDepositId),
ADD INDEX IX_CheckTreasuryVoucher_CheckNumber (CheckNumber),
ADD INDEX IX_CheckTreasuryVoucher_Date_Status (TransactionDate, CheckStatusId);

-- 2. Update CheckStatusHistory table
ALTER TABLE CheckStatusHistory
ADD INDEX IX_CheckStatusHistory_Check_Date (CheckTreasuryVoucherId, TransactionDate),
ADD INDEX IX_CheckStatusHistory_Deposit (CheckDepositId),
ADD INDEX IX_CheckStatusHistory_StatusTransition (CheckStatusFromId, CheckStatusToId);

-- 3. Update foreign key constraints
ALTER TABLE CheckTreasuryVoucher 
DROP FOREIGN KEY FK_CheckTreasuryVoucher_CheckDeposit,
ADD CONSTRAINT FK_CheckTreasuryVoucher_CheckDeposit 
    FOREIGN KEY (CheckDepositId) REFERENCES CheckDeposit(Id) ON DELETE RESTRICT;

ALTER TABLE CheckStatusHistory 
DROP FOREIGN KEY FK_CheckStatusHistory_CheckDeposit,
ADD CONSTRAINT FK_CheckStatusHistory_CheckDeposit 
    FOREIGN KEY (CheckDepositId) REFERENCES CheckDeposit(Id) ON DELETE RESTRICT;
```

### 2. Verify Database Schema
```bash
# Run the following command to verify schema
dotnet ef database update --project SimpleBooks.Repositories.EF
```

## Application Configuration

### 1. Update appsettings.json
```json
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=localhost;Database=SimpleBooks;Uid=root;Pwd=yourpassword;",
    "RedisConnection": "localhost:6379"
  },
  "CheckManagement": {
    "EnableAuditTrail": true,
    "EnableRealTimeUpdates": true,
    "MaxBulkOperationSize": 100,
    "DefaultTimeoutMinutes": 30,
    "EnablePerformanceLogging": true
  },
  "Security": {
    "RequireHttps": true,
    "EnableRoleBasedAccess": true,
    "SessionTimeoutMinutes": 60,
    "MaxLoginAttempts": 5
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "SimpleBooks.Services": "Debug",
      "SimpleBooks.Domain": "Debug"
    }
  }
}
```

### 2. Register Services in Program.cs
```csharp
// Add to Program.cs or Startup.cs

// Register new services
builder.Services.AddScoped<ICheckLifecycleService, CheckLifecycleService>();
builder.Services.AddScoped<ICheckLifecycleRepository, CheckLifecycleRepository>();

// Configure authorization policies
builder.Services.AddAuthorization(options =>
{
    options.AddPolicy(CheckManagementPolicies.CanViewChecks, policy =>
        policy.RequireClaim("permission", CheckManagementPermissions.ViewChecks));
    
    options.AddPolicy(CheckManagementPolicies.CanDepositChecks, policy =>
        policy.RequireClaim("permission", CheckManagementPermissions.DepositChecks));
    
    options.AddPolicy(CheckManagementPolicies.CanCollectChecks, policy =>
        policy.RequireClaim("permission", CheckManagementPermissions.CollectChecks));
    
    options.AddPolicy(CheckManagementPolicies.CanRejectChecks, policy =>
        policy.RequireClaim("permission", CheckManagementPermissions.RejectChecks));
    
    options.AddPolicy(CheckManagementPolicies.CanRemoveFromDeposit, policy =>
        policy.RequireClaim("permission", CheckManagementPermissions.RemoveFromDeposit));
    
    options.AddPolicy(CheckManagementPolicies.CanViewAuditTrail, policy =>
        policy.RequireClaim("permission", CheckManagementPermissions.ViewAuditTrail));
    
    options.AddPolicy(CheckManagementPolicies.CanViewReports, policy =>
        policy.RequireClaim("permission", CheckManagementPermissions.ViewReports));
});

// Register authorization handlers
builder.Services.AddScoped<IAuthorizationHandler, CheckOperationAuthorizationHandler>();
builder.Services.AddScoped<IAuthorizationHandler, CheckOwnershipAuthorizationHandler>();
builder.Services.AddScoped<IAuthorizationHandler, TimeBasedOperationAuthorizationHandler>();
```

## Deployment Steps

### 1. Build and Publish
```bash
# Clean and build solution
dotnet clean
dotnet build --configuration Release

# Publish API project
dotnet publish SimpleBooks.API --configuration Release --output ./publish/api

# Publish Web project
dotnet publish SimpleBooks.WEB --configuration Release --output ./publish/web
```

### 2. Deploy to IIS (Windows)

#### API Deployment
1. Create new IIS site for API:
   - Site name: `SimpleBooks.API`
   - Physical path: `C:\inetpub\wwwroot\SimpleBooks.API`
   - Port: `5001`
   - Enable HTTPS

2. Copy published files to IIS directory
3. Configure application pool:
   - .NET CLR Version: No Managed Code
   - Managed Pipeline Mode: Integrated
   - Identity: ApplicationPoolIdentity

#### Web Deployment
1. Create new IIS site for Web:
   - Site name: `SimpleBooks.WEB`
   - Physical path: `C:\inetpub\wwwroot\SimpleBooks.WEB`
   - Port: `443` (HTTPS)

2. Copy published files to IIS directory
3. Configure same application pool settings as API

### 3. Configure SSL Certificates
```bash
# For development, create self-signed certificate
dotnet dev-certs https --trust

# For production, install proper SSL certificate in IIS
```

## Security Configuration

### 1. User Roles and Permissions Setup
```sql
-- Insert default roles
INSERT INTO Roles (Id, Name, Description) VALUES 
(UNHEX(REPLACE(UUID(), '-', '')), 'CheckViewer', 'Can view checks and deposits'),
(UNHEX(REPLACE(UUID(), '-', '')), 'CheckOperator', 'Can perform check operations'),
(UNHEX(REPLACE(UUID(), '-', '')), 'CheckSupervisor', 'Can manage deposits and override rules'),
(UNHEX(REPLACE(UUID(), '-', '')), 'CheckAdministrator', 'Full check management access');

-- Insert permissions
INSERT INTO Permissions (Id, Name, Description) VALUES
(UNHEX(REPLACE(UUID(), '-', '')), 'Permissions.CheckManagement.ViewChecks', 'View checks'),
(UNHEX(REPLACE(UUID(), '-', '')), 'Permissions.CheckManagement.DepositChecks', 'Deposit checks'),
(UNHEX(REPLACE(UUID(), '-', '')), 'Permissions.CheckManagement.CollectChecks', 'Collect checks'),
(UNHEX(REPLACE(UUID(), '-', '')), 'Permissions.CheckManagement.RejectChecks', 'Reject checks'),
(UNHEX(REPLACE(UUID(), '-', '')), 'Permissions.CheckManagement.RemoveFromDeposit', 'Remove from deposit'),
(UNHEX(REPLACE(UUID(), '-', '')), 'Permissions.CheckManagement.ViewAuditTrail', 'View audit trail'),
(UNHEX(REPLACE(UUID(), '-', '')), 'Permissions.CheckManagement.ViewReports', 'View reports');
```

### 2. Configure HTTPS Redirection
```csharp
// In Program.cs
if (!app.Environment.IsDevelopment())
{
    app.UseHttpsRedirection();
    app.UseHsts();
}
```

## Performance Optimization

### 1. Database Optimization
```sql
-- Analyze table performance
ANALYZE TABLE CheckTreasuryVoucher, CheckDeposit, CheckStatusHistory;

-- Optimize tables
OPTIMIZE TABLE CheckTreasuryVoucher, CheckDeposit, CheckStatusHistory;

-- Check index usage
SHOW INDEX FROM CheckTreasuryVoucher;
```

### 2. Application Performance
```csharp
// Configure caching in Program.cs
builder.Services.AddMemoryCache();
builder.Services.AddResponseCaching();

// Configure compression
builder.Services.AddResponseCompression(options =>
{
    options.EnableForHttps = true;
});
```

## Monitoring and Logging

### 1. Configure Application Insights (Optional)
```json
{
  "ApplicationInsights": {
    "InstrumentationKey": "your-instrumentation-key"
  }
}
```

### 2. Configure Structured Logging
```csharp
// In Program.cs
builder.Services.AddLogging(logging =>
{
    logging.AddConsole();
    logging.AddDebug();
    logging.AddEventSourceLogger();
});
```

## Testing Deployment

### 1. Smoke Tests
```bash
# Test API endpoints
curl -k https://localhost:5001/api/CheckLifecycle/validate-deposit -X POST -H "Content-Type: application/json" -d '{"CheckIds":[]}'

# Test Web application
curl -k https://localhost/CheckLifecycle
```

### 2. Load Testing
```bash
# Install Apache Bench or similar tool
ab -n 100 -c 10 https://localhost:5001/api/CheckLifecycle/validate-deposit
```

## Backup and Recovery

### 1. Database Backup
```bash
# Create backup script
mysqldump -u root -p SimpleBooks > backup_$(date +%Y%m%d_%H%M%S).sql
```

### 2. Application Backup
```bash
# Backup application files
tar -czf simpleBooks_backup_$(date +%Y%m%d_%H%M%S).tar.gz /path/to/application
```

## Troubleshooting

### Common Issues

1. **Database Connection Issues**
   - Verify connection string
   - Check MySQL service status
   - Verify user permissions

2. **Authorization Issues**
   - Check user roles and permissions
   - Verify policy configuration
   - Check claims in JWT tokens

3. **Performance Issues**
   - Check database indexes
   - Monitor memory usage
   - Review slow query logs

### Log Locations
- Application logs: `%TEMP%\SimpleBooks\logs\`
- IIS logs: `C:\inetpub\logs\LogFiles\`
- MySQL logs: MySQL data directory

## Maintenance

### Regular Tasks
1. **Daily**: Monitor application logs
2. **Weekly**: Check database performance
3. **Monthly**: Update security patches
4. **Quarterly**: Review and optimize indexes

### Updates and Patches
```bash
# Update application
git pull origin main
dotnet build --configuration Release
# Deploy following deployment steps
```

## Support and Documentation

### Additional Resources
- API Documentation: `/swagger` endpoint
- User Manual: Available in `/docs` folder
- Technical Support: Contact system administrator

### Monitoring Dashboards
- Application Performance: Available at `/monitoring`
- Database Performance: Use MySQL Workbench
- System Health: Check IIS Manager

This deployment guide ensures a smooth transition to the refactored check management system with all new features properly configured and secured.
