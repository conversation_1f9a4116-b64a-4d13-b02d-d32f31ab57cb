﻿namespace SimpleBooks.Models.Model.Treasury.BankManagement.CheckManagement
{
    [Table("CheckStatusHistory")]
    [TypeDescriptionProvider(typeof(BaseTypeDescriptionProvider<CheckStatusHistoryModel>))]
    public partial class CheckStatusHistoryModel : BaseModel
    {
        [CustomRequired]
        [DisplayName("Transaction Date")]
        [Column(TypeName = "Date"), DataType(DataType.Date)]
        public DateTime TransactionDate { get; set; }

        [CustomRequired]
        [DisplayName("Note")]
        public string Note { get; set; }

        [CustomRequired]
        [DisplayName("Check Status From")]
        public Ulid CheckStatusFromId { get; set; }
        public virtual CheckStatusModel? CheckStatusFrom { get; set; }

        [CustomRequired]
        [DisplayName("Check Status To")]
        public Ulid CheckStatusToId { get; set; }
        public virtual CheckStatusModel? CheckStatusTo { get; set; }

        [CustomRequired]
        [DisplayName("Check Treasury Voucher")]
        public Ulid CheckTreasuryVoucherId { get; set; }
        public virtual CheckTreasuryVoucherModel? CheckTreasuryVoucher { get; set; }

        [DisplayName("Check Deposit")]
        public Ulid? CheckDepositId { get; set; }
        public virtual CheckDepositModel? CheckDeposit { get; set; }

        [DisplayName("Check Collection")]
        public Ulid? CheckCollectionId { get; set; }
        public virtual CheckCollectionModel? CheckCollection { get; set; }

        [DisplayName("Check Reject")]
        public Ulid? CheckRejectId { get; set; }
        public virtual CheckRejectModel? CheckReject { get; set; }
    }
}
