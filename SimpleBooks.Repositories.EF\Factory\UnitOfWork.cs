﻿namespace SimpleBooks.Repositories.EF.Factory
{
    public class UnitOfWork : IUnitOfWork, IDisposable
    {
        private ApplicationDBContext _context;

        public UnitOfWork(IDatabaseService databaseService)
        {
            _context = databaseService.GetDbContext();
            #region HR
            Employee = new EmployeeRepository(_context);
            #endregion
            #region Purchases
            Vendor = new VendorRepository(_context);
            VendorType = new VendorTypeRepository(_context);
            Bill = new BillRepository(_context);
            BillReturn = new BillReturnRepository(_context);
            #endregion
            #region Sales
            Customer = new CustomerRepository(_context);
            CustomerType = new CustomerTypeRepository(_context);
            Invoice = new InvoiceRepository(_context);
            InvoiceReturn = new InvoiceReturnRepository(_context);
            #endregion
            #region Tax 
            TaxType = new TaxTypeRepository(_context);
            TaxSubType = new TaxSubTypeRepository(_context);
            #endregion
            #region Treasury
            BankAccount = new BankAccountRepository(_context);
            Bank = new BankRepository(_context);
            BankTransferTreasuryVoucher = new BankTransferTreasuryVoucherRepository(_context);
            CheckCollection = new CheckCollectionRepository(_context);
            CheckDeposit = new CheckDepositRepository(_context);
            CheckReject = new CheckRejectRepository(_context);
            CheckStatusHistory = new CheckStatusHistoryRepository(_context);
            CheckTreasuryVoucher = new CheckTreasuryVoucherRepository(_context);
            CheckVaultLocation = new CheckVaultLocationRepository(_context);
            CheckVault = new CheckVaultRepository(_context);
            CashTreasuryVoucher = new CashTreasuryVoucherRepository(_context);
            DrawerLocation = new DrawerLocationRepository(_context);
            Drawer = new DrawerRepository(_context);
            Expenses = new ExpensesRepository(_context);
            PaymentTerm = new PaymentTermRepository(_context);
            TreasuryLine = new TreasuryLineRepository(_context);
            #endregion
            #region Warehouse
            Inventory = new InventoryRepository(_context);
            InventoryTax = new InventoryTaxRepository(_context);
            ProductCategory = new ProductCategoryRepository(_context);
            Product = new ProductRepository(_context);
            ProductTax = new ProductTaxRepository(_context);
            ProductType = new ProductTypeRepository(_context);
            ProductUnit = new ProductUnitRepository(_context);
            Store = new StoreRepository(_context);
            Unit = new UnitRepository(_context);
            #endregion
            #region User
            Audit = new AuditRepository(_context);
            RefreshToken = new RefreshTokenRepository(_context);
            ScreensAccessProfile = new ScreensAccessProfileRepository(_context);
            Setting = new SettingRepository(_context);
            User = new UserRepository(_context);
            #endregion
            TransactionType = new TransactionTypeRepository(_context);
        }

        #region HR
        public IBaseRepository<EmployeeModel, EmployeeModel> Employee { get; private set; }
        #endregion

        #region Purchases
        public IBaseRepository<VendorModel, IndexVendorViewModel> Vendor { get; private set; }
        public IBaseRepository<VendorTypeModel, VendorTypeModel> VendorType { get; private set; }
        public IBaseRepository<BillModel, IndexBillViewModel> Bill { get; private set; }
        public IBaseRepository<BillReturnModel, IndexBillReturnViewModel> BillReturn { get; private set; }
        #endregion

        #region Sales
        public IBaseRepository<CustomerModel, IndexCustomerViewModel> Customer { get; private set; }
        public IBaseRepository<CustomerTypeModel, CustomerTypeModel> CustomerType { get; private set; }
        public IBaseRepository<InvoiceModel, IndexInvoiceViewModel> Invoice { get; private set; }
        public IBaseRepository<InvoiceReturnModel, IndexInvoiceReturnViewModel> InvoiceReturn { get; private set; }
        #endregion

        #region Tax 
        public ITaxTypeRepository<TaxTypeModel, TaxTypeModel> TaxType { get; private set; }
        public ITaxSubTypeRepository<TaxSubTypeModel, TaxSubTypeModel> TaxSubType { get; private set; }
        #endregion

        #region Treasury 
        public IBaseRepository<BankAccountModel, BankAccountModel> BankAccount { get; private set; }
        public IBaseRepository<BankModel, BankModel> Bank { get; private set; }
        public IBankTransferTreasuryVoucherRepository<BankTransferTreasuryVoucherModel, BankTransferTreasuryVoucherModel> BankTransferTreasuryVoucher { get; private set; }
        public IBaseRepository<CheckCollectionModel, CheckCollectionModel> CheckCollection { get; private set; }
        public IBaseRepository<CheckDepositModel, CheckDepositModel> CheckDeposit { get; private set; }
        public IBaseRepository<CheckRejectModel, CheckRejectModel> CheckReject { get; private set; }
        public IBaseRepository<CheckStatusHistoryModel, CheckStatusHistoryModel> CheckStatusHistory { get; private set; }
        public ICheckTreasuryVoucherRepository<CheckTreasuryVoucherModel, CheckTreasuryVoucherModel> CheckTreasuryVoucher { get; private set; }
        public IBaseRepository<CheckVaultLocationModel, CheckVaultLocationModel> CheckVaultLocation { get; private set; }
        public IBaseRepository<CheckVaultModel, CheckVaultModel> CheckVault { get; private set; }
        public ICashTreasuryVoucherRepository<CashTreasuryVoucherModel, CashTreasuryVoucherModel> CashTreasuryVoucher { get; private set; }
        public IBaseRepository<DrawerLocationModel, DrawerLocationModel> DrawerLocation { get; private set; }
        public IBaseRepository<DrawerModel, DrawerModel> Drawer { get; private set; }
        public IBaseRepository<ExpensesModel, ExpensesModel> Expenses { get; private set; }
        public IPaymentTermRepository<PaymentTermModel, PaymentTermModel> PaymentTerm { get; private set; }
        public ITreasuryLineRepository<TreasuryLineModel, TreasuryLineModel> TreasuryLine { get; private set; }
        #endregion

        #region User
        public IAuditRepository<AuditModel> Audit { get; private set; }
        public IBaseRepository<ScreensAccessProfileModel, ScreensAccessProfileModel> ScreensAccessProfile { get; private set; }
        public IRefreshTokenRepository<RefreshTokenModel, RefreshTokenModel> RefreshToken { get; private set; }
        public IBaseRepository<SettingModel, SettingModel> Setting { get; private set; }
        public IUserRepository<UserModel, UserModel> User { get; private set; }
        #endregion

        #region Warehouse
        public IBaseRepository<InventoryModel, InventoryModel> Inventory { get; private set; }
        public IBaseRepository<InventoryTaxModel, InventoryTaxModel> InventoryTax { get; private set; }
        public IBaseRepository<ProductCategoryModel, ProductCategoryModel> ProductCategory { get; private set; }
        public IBaseRepository<ProductModel, IndexProductViewModel> Product { get; private set; }
        public IBaseRepository<ProductTaxModel, ProductTaxModel> ProductTax { get; private set; }
        public IBaseRepository<ProductTypeModel, ProductTypeModel> ProductType { get; private set; }
        public IBaseRepository<ProductUnitModel, ProductUnitModel> ProductUnit { get; private set; }
        public IBaseRepository<StoreModel, StoreModel> Store { get; private set; }
        public IBaseRepository<UnitModel, UnitModel> Unit { get; private set; }
        #endregion

        public IBaseRepository<TransactionTypeModel, TransactionTypeModel> TransactionType { get; private set; }

        public void ExecuteQuery(string Query)
        {
            if (!string.IsNullOrEmpty(Query))
            {
                FormattableString formattableString = FormattableStringFactory.Create(Query);
                _context.Database.ExecuteSql(formattableString);
            }
        }

        public void Save()
        {
            _context.SaveChanges();
        }

        private bool disposed = false;

        protected virtual void Dispose(bool disposing)
        {
            if (!this.disposed)
            {
                if (disposing)
                {
                    _context.Dispose();
                }
            }
            this.disposed = true;
        }

        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }
    }
}
