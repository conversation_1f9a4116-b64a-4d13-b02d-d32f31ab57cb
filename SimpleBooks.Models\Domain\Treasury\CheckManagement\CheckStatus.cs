using SimpleBooks.Models.Enumerations;

namespace SimpleBooks.Models.Domain.Treasury.CheckManagement
{
    /// <summary>
    /// Value object representing check status with business rules
    /// </summary>
    public class CheckStatus : IEquatable<CheckStatus>
    {
        public Ulid Value { get; private set; }
        public string Name { get; private set; }

        private CheckStatus(Ulid value, string name)
        {
            Value = value;
            Name = name;
        }

        public static CheckStatus Unknown => new(CheckStatusEnumeration.Unknown.Value, CheckStatusEnumeration.Unknown.Name);
        public static CheckStatus Issued => new(CheckStatusEnumeration.Issued.Value, CheckStatusEnumeration.Issued.Name);
        public static CheckStatus Received => new(CheckStatusEnumeration.Received.Value, CheckStatusEnumeration.Received.Name);
        public static CheckStatus Deposited => new(CheckStatusEnumeration.Deposited.Value, CheckStatusEnumeration.Deposited.Name);
        public static CheckStatus Cleared => new(CheckStatusEnumeration.Cleared.Value, CheckStatusEnumeration.Cleared.Name);
        public static CheckStatus Returned => new(CheckStatusEnumeration.Returned.Value, CheckStatusEnumeration.Returned.Name);
        public static CheckStatus Rejected => new(CheckStatusEnumeration.Rejected.Value, CheckStatusEnumeration.Rejected.Name);

        public static CheckStatus FromValue(Ulid value)
        {
            return value switch
            {
                var v when v == CheckStatusEnumeration.Unknown.Value => Unknown,
                var v when v == CheckStatusEnumeration.Issued.Value => Issued,
                var v when v == CheckStatusEnumeration.Received.Value => Received,
                var v when v == CheckStatusEnumeration.Deposited.Value => Deposited,
                var v when v == CheckStatusEnumeration.Cleared.Value => Cleared,
                var v when v == CheckStatusEnumeration.Returned.Value => Returned,
                var v when v == CheckStatusEnumeration.Rejected.Value => Rejected,
                _ => throw new ArgumentException($"Invalid check status value: {value}")
            };
        }

        public bool IsInitialStatus() => this == Received || this == Issued || this == Unknown;

        public bool CanTransitionTo(CheckStatus targetStatus, Ulid transactionType)
        {
            // For received checks (TreasuryCheckIn)
            if (transactionType == TransactionTypeEnumeration.TreasuryCheckIn.Value)
            {
                return this switch
                {
                    var s when s == Received => targetStatus == Deposited,
                    var s when s == Deposited => targetStatus == Cleared || targetStatus == Rejected,
                    var s when s == Rejected => targetStatus == Returned,
                    _ => false
                };
            }

            // For issued checks (TreasuryCheckOut)
            if (transactionType == TransactionTypeEnumeration.TreasuryCheckOut.Value)
            {
                return this switch
                {
                    var s when s == Issued => targetStatus == Cleared || targetStatus == Returned,
                    _ => false
                };
            }

            return false;
        }

        public bool CanBeRemovedFromDeposit() => this == Deposited;

        public bool IsFinalStatus() => this == Cleared || this == Rejected || this == Returned;

        public bool Equals(CheckStatus? other)
        {
            if (other is null) return false;
            return Value == other.Value;
        }

        public override bool Equals(object? obj) => Equals(obj as CheckStatus);

        public override int GetHashCode() => Value.GetHashCode();

        public static bool operator ==(CheckStatus? left, CheckStatus? right) => 
            left?.Equals(right) ?? right is null;

        public static bool operator !=(CheckStatus? left, CheckStatus? right) => 
            !(left == right);

        public override string ToString() => Name;
    }
}
