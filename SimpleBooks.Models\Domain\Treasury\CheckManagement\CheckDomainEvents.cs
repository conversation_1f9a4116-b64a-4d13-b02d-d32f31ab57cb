namespace SimpleBooks.Models.Domain.Treasury.CheckManagement
{
    /// <summary>
    /// Base class for check domain events
    /// </summary>
    public abstract class CheckDomainEvent
    {
        public Ulid CheckId { get; }
        public DateTime OccurredAt { get; }

        protected CheckDomainEvent(Ulid checkId)
        {
            CheckId = checkId;
            OccurredAt = DateTime.UtcNow;
        }
    }

    /// <summary>
    /// Event raised when a check status changes
    /// </summary>
    public class CheckStatusChangedEvent : CheckDomainEvent
    {
        public CheckStatus FromStatus { get; }
        public CheckStatus ToStatus { get; }
        public string Note { get; }
        public Ulid? DepositId { get; }
        public Ulid? CollectionId { get; }
        public Ulid? RejectId { get; }

        public CheckStatusChangedEvent(
            Ulid checkId, 
            CheckStatus fromStatus, 
            CheckStatus toStatus, 
            string note,
            Ulid? depositId = null,
            Ulid? collectionId = null,
            Ulid? rejectId = null) : base(checkId)
        {
            FromStatus = fromStatus;
            ToStatus = toStatus;
            Note = note;
            DepositId = depositId;
            CollectionId = collectionId;
            RejectId = rejectId;
        }
    }

    /// <summary>
    /// Event raised when a check is deposited
    /// </summary>
    public class CheckDepositedEvent : CheckDomainEvent
    {
        public Ulid DepositId { get; }
        public DateTime DepositDate { get; }

        public CheckDepositedEvent(Ulid checkId, Ulid depositId, DateTime depositDate) : base(checkId)
        {
            DepositId = depositId;
            DepositDate = depositDate;
        }
    }

    /// <summary>
    /// Event raised when a check is removed from deposit
    /// </summary>
    public class CheckRemovedFromDepositEvent : CheckDomainEvent
    {
        public Ulid DepositId { get; }
        public string Reason { get; }

        public CheckRemovedFromDepositEvent(Ulid checkId, Ulid depositId, string reason) : base(checkId)
        {
            DepositId = depositId;
            Reason = reason;
        }
    }

    /// <summary>
    /// Event raised when a check is collected
    /// </summary>
    public class CheckCollectedEvent : CheckDomainEvent
    {
        public Ulid CollectionId { get; }
        public DateTime CollectionDate { get; }

        public CheckCollectedEvent(Ulid checkId, Ulid collectionId, DateTime collectionDate) : base(checkId)
        {
            CollectionId = collectionId;
            CollectionDate = collectionDate;
        }
    }

    /// <summary>
    /// Event raised when a check is rejected
    /// </summary>
    public class CheckRejectedEvent : CheckDomainEvent
    {
        public Ulid RejectId { get; }
        public DateTime RejectDate { get; }
        public string Reason { get; }

        public CheckRejectedEvent(Ulid checkId, Ulid rejectId, DateTime rejectDate, string reason) : base(checkId)
        {
            RejectId = rejectId;
            RejectDate = rejectDate;
            Reason = reason;
        }
    }
}
