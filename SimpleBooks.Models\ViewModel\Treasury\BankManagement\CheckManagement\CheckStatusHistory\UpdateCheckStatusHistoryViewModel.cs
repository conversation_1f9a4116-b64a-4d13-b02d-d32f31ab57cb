﻿namespace SimpleBooks.Models.ViewModel.Treasury.BankManagement.CheckManagement.CheckStatusHistory
{
    public class UpdateCheckStatusHistoryViewModel : BaseUpdateViewModel, IEntityMapper<CheckStatusHistoryModel, UpdateCheckStatusHistoryViewModel>
    {
        [CustomRequired]
        [DisplayName("Transaction Date")]
        [Column(TypeName = "Date"), DataType(DataType.Date)]
        public DateTime TransactionDate { get; set; }
        [CustomRequired]
        [DisplayName("Note")]
        public string Note { get; set; }
        [CustomRequired]
        [DisplayName("Check Status From")]
        public Ulid CheckStatusFromId { get; set; }
        [CustomRequired]
        [DisplayName("Check Status To")]
        public Ulid CheckStatusToId { get; set; }
        [CustomRequired]
        [DisplayName("Check Treasury Voucher")]
        public Ulid CheckTreasuryVoucherId { get; set; }
        [DisplayName("Check Deposit")]
        public Ulid? CheckDepositId { get; set; }
        [DisplayName("Check Collection")]
        public Ulid? CheckCollectionId { get; set; }
        [DisplayName("Check Reject")]
        public Ulid? CheckRejectId { get; set; }

        public UpdateCheckStatusHistoryViewModel ToDto(CheckStatusHistoryModel entity) => entity.ToUpdateDto();

        public CheckStatusHistoryModel ToEntity() => CheckStatusHistoryMapper.ToEntity(this);
    }
}
