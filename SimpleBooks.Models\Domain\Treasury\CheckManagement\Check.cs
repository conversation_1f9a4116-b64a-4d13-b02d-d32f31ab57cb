using SimpleBooks.Models.Model.Treasury.BankManagement.CheckManagement;

namespace SimpleBooks.Models.Domain.Treasury.CheckManagement
{
    /// <summary>
    /// Domain entity representing a check with business logic and invariants
    /// </summary>
    public class Check
    {
        private readonly List<CheckDomainEvent> _domainEvents = new();
        
        public Ulid Id { get; private set; }
        public string CheckNumber { get; private set; }
        public DateTime DueDate { get; private set; }
        public string IssuerName { get; private set; }
        public string BearerName { get; private set; }
        public decimal Amount { get; private set; }
        public CheckStatus Status { get; private set; }
        public Ulid TransactionTypeId { get; private set; }
        public Ulid? DepositId { get; private set; }
        public DateTime TransactionDate { get; private set; }
        public string Note { get; private set; }

        // For EF Core
        private Check() { }

        public Check(
            Ulid id,
            string checkNumber,
            DateTime dueDate,
            string issuerName,
            string bearerName,
            decimal amount,
            Ulid transactionTypeId,
            DateTime transactionDate,
            string note = "")
        {
            if (string.IsNullOrWhiteSpace(checkNumber))
                throw new ArgumentException("Check number cannot be empty", nameof(checkNumber));
            if (string.IsNullOrWhiteSpace(issuerName))
                throw new ArgumentException("Issuer name cannot be empty", nameof(issuerName));
            if (string.IsNullOrWhiteSpace(bearerName))
                throw new ArgumentException("Bearer name cannot be empty", nameof(bearerName));
            if (amount <= 0)
                throw new ArgumentException("Amount must be positive", nameof(amount));

            Id = id == Ulid.Empty ? Ulid.NewUlid() : id;
            CheckNumber = checkNumber;
            DueDate = dueDate;
            IssuerName = issuerName;
            BearerName = bearerName;
            Amount = amount;
            TransactionTypeId = transactionTypeId;
            TransactionDate = transactionDate;
            Note = note ?? string.Empty;

            // Set initial status based on transaction type
            Status = CheckStatusEnumeration.InitialCheckStatus(transactionTypeId) switch
            {
                var s when s == CheckStatusEnumeration.Received.Value => CheckStatus.Received,
                var s when s == CheckStatusEnumeration.Issued.Value => CheckStatus.Issued,
                _ => CheckStatus.Unknown
            };
        }

        public static Check FromModel(CheckTreasuryVoucherModel model)
        {
            var check = new Check
            {
                Id = model.Id,
                CheckNumber = model.CheckNumber,
                DueDate = model.DueDate,
                IssuerName = model.IssuerName,
                BearerName = model.BearerName,
                Amount = model.Amount,
                Status = CheckStatus.FromValue(model.CheckStatusId),
                TransactionTypeId = model.TransactionTypeId,
                DepositId = model.CheckDepositId,
                TransactionDate = model.TransactionDate,
                Note = model.Note ?? string.Empty
            };

            return check;
        }

        public CheckTreasuryVoucherModel ToModel()
        {
            return new CheckTreasuryVoucherModel
            {
                Id = Id,
                CheckNumber = CheckNumber,
                DueDate = DueDate,
                IssuerName = IssuerName,
                BearerName = BearerName,
                Amount = Amount,
                CheckStatusId = Status.Value,
                TransactionTypeId = TransactionTypeId,
                CheckDepositId = DepositId,
                TransactionDate = TransactionDate,
                Note = Note
            };
        }

        public void DepositToBank(Ulid depositId, DateTime depositDate, string note = "")
        {
            if (!Status.CanTransitionTo(CheckStatus.Deposited, TransactionTypeId))
                throw new InvalidOperationException($"Cannot deposit check in {Status.Name} status");

            if (DepositId.HasValue)
                throw new InvalidOperationException("Check is already deposited");

            var oldStatus = Status;
            Status = CheckStatus.Deposited;
            DepositId = depositId;

            var eventNote = string.IsNullOrWhiteSpace(note) 
                ? $"Check deposited on {depositDate:dd/MM/yyyy}" 
                : note;

            _domainEvents.Add(new CheckStatusChangedEvent(Id, oldStatus, Status, eventNote, depositId));
            _domainEvents.Add(new CheckDepositedEvent(Id, depositId, depositDate));
        }

        public void RemoveFromDeposit(string reason = "")
        {
            if (!Status.CanBeRemovedFromDeposit())
                throw new InvalidOperationException($"Cannot remove check from deposit in {Status.Name} status. Only deposited checks can be removed.");

            if (!DepositId.HasValue)
                throw new InvalidOperationException("Check is not deposited");

            var oldDepositId = DepositId.Value;
            var oldStatus = Status;
            Status = CheckStatus.Received;
            DepositId = null;

            var eventNote = string.IsNullOrWhiteSpace(reason) 
                ? "Check removed from deposit" 
                : $"Check removed from deposit: {reason}";

            _domainEvents.Add(new CheckStatusChangedEvent(Id, oldStatus, Status, eventNote));
            _domainEvents.Add(new CheckRemovedFromDepositEvent(Id, oldDepositId, reason));
        }

        public void MarkAsCollected(Ulid collectionId, DateTime collectionDate)
        {
            if (!Status.CanTransitionTo(CheckStatus.Cleared, TransactionTypeId))
                throw new InvalidOperationException($"Cannot collect check in {Status.Name} status");

            if (!DepositId.HasValue)
                throw new InvalidOperationException("Check must be deposited before collection");

            var oldStatus = Status;
            Status = CheckStatus.Cleared;

            var note = $"Check collected on {collectionDate:dd/MM/yyyy}";
            _domainEvents.Add(new CheckStatusChangedEvent(Id, oldStatus, Status, note, DepositId, collectionId));
            _domainEvents.Add(new CheckCollectedEvent(Id, collectionId, collectionDate));
        }

        public void MarkAsRejected(Ulid rejectId, DateTime rejectDate, string reason)
        {
            if (!Status.CanTransitionTo(CheckStatus.Rejected, TransactionTypeId))
                throw new InvalidOperationException($"Cannot reject check in {Status.Name} status");

            if (!DepositId.HasValue)
                throw new InvalidOperationException("Check must be deposited before rejection");

            if (string.IsNullOrWhiteSpace(reason))
                throw new ArgumentException("Rejection reason is required", nameof(reason));

            var oldStatus = Status;
            Status = CheckStatus.Rejected;

            var note = $"Check rejected on {rejectDate:dd/MM/yyyy}: {reason}";
            _domainEvents.Add(new CheckStatusChangedEvent(Id, oldStatus, Status, note, DepositId, null, rejectId));
            _domainEvents.Add(new CheckRejectedEvent(Id, rejectId, rejectDate, reason));
        }

        public IReadOnlyList<CheckDomainEvent> GetDomainEvents() => _domainEvents.AsReadOnly();

        public void ClearDomainEvents() => _domainEvents.Clear();

        public bool CanBeModified() => !Status.IsFinalStatus();

        public bool IsInDeposit() => DepositId.HasValue && Status == CheckStatus.Deposited;
    }
}
