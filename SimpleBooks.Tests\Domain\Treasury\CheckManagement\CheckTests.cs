using SimpleBooks.Models.Domain.Treasury.CheckManagement;
using SimpleBooks.Models.Enumerations;
using Xunit;

namespace SimpleBooks.Tests.Domain.Treasury.CheckManagement
{
    public class CheckTests
    {
        private Check CreateTestCheck(CheckStatus? initialStatus = null)
        {
            return new Check(
                Ulid.NewUlid(),
                "CHK001",
                DateTime.Today.AddDays(30),
                "Test Issuer",
                "Test Bearer",
                1000m,
                TransactionTypeEnumeration.TreasuryCheckIn.Value,
                DateTime.Today,
                "Test check"
            );
        }

        [Fact]
        public void Check_Constructor_ValidData_ShouldCreateCheck()
        {
            // Arrange & Act
            var check = CreateTestCheck();

            // Assert
            Assert.NotEqual(Ulid.Empty, check.Id);
            Assert.Equal("CHK001", check.CheckNumber);
            Assert.Equal(1000m, check.Amount);
            Assert.Equal(CheckStatus.Received, check.Status);
            Assert.Null(check.DepositId);
        }

        [Theory]
        [InlineData("", "Check number cannot be empty")]
        [InlineData(null, "Check number cannot be empty")]
        public void Check_Constructor_InvalidCheckNumber_ShouldThrowException(string checkNumber, string expectedMessage)
        {
            // Act & Assert
            var exception = Assert.Throws<ArgumentException>(() => new Check(
                Ulid.NewUlid(),
                checkNumber,
                DateTime.Today.AddDays(30),
                "Test Issuer",
                "Test Bearer",
                1000m,
                TransactionTypeEnumeration.TreasuryCheckIn.Value,
                DateTime.Today
            ));

            Assert.Contains(expectedMessage, exception.Message);
        }

        [Theory]
        [InlineData("", "Issuer name cannot be empty")]
        [InlineData(null, "Issuer name cannot be empty")]
        public void Check_Constructor_InvalidIssuerName_ShouldThrowException(string issuerName, string expectedMessage)
        {
            // Act & Assert
            var exception = Assert.Throws<ArgumentException>(() => new Check(
                Ulid.NewUlid(),
                "CHK001",
                DateTime.Today.AddDays(30),
                issuerName,
                "Test Bearer",
                1000m,
                TransactionTypeEnumeration.TreasuryCheckIn.Value,
                DateTime.Today
            ));

            Assert.Contains(expectedMessage, exception.Message);
        }

        [Theory]
        [InlineData(0)]
        [InlineData(-100)]
        public void Check_Constructor_InvalidAmount_ShouldThrowException(decimal amount)
        {
            // Act & Assert
            var exception = Assert.Throws<ArgumentException>(() => new Check(
                Ulid.NewUlid(),
                "CHK001",
                DateTime.Today.AddDays(30),
                "Test Issuer",
                "Test Bearer",
                amount,
                TransactionTypeEnumeration.TreasuryCheckIn.Value,
                DateTime.Today
            ));

            Assert.Contains("Amount must be positive", exception.Message);
        }

        [Fact]
        public void Check_DepositToBank_ValidDeposit_ShouldUpdateStatusAndRaiseDomainEvents()
        {
            // Arrange
            var check = CreateTestCheck();
            var depositId = Ulid.NewUlid();
            var depositDate = DateTime.Today;

            // Act
            check.DepositToBank(depositId, depositDate);

            // Assert
            Assert.Equal(CheckStatus.Deposited, check.Status);
            Assert.Equal(depositId, check.DepositId);
            
            var domainEvents = check.GetDomainEvents();
            Assert.Equal(2, domainEvents.Count);
            Assert.Contains(domainEvents, e => e is CheckStatusChangedEvent);
            Assert.Contains(domainEvents, e => e is CheckDepositedEvent);
        }

        [Fact]
        public void Check_DepositToBank_AlreadyDeposited_ShouldThrowException()
        {
            // Arrange
            var check = CreateTestCheck();
            var depositId1 = Ulid.NewUlid();
            var depositId2 = Ulid.NewUlid();
            check.DepositToBank(depositId1, DateTime.Today);

            // Act & Assert
            var exception = Assert.Throws<InvalidOperationException>(() => 
                check.DepositToBank(depositId2, DateTime.Today));
            
            Assert.Contains("already deposited", exception.Message);
        }

        [Fact]
        public void Check_RemoveFromDeposit_ValidRemoval_ShouldRevertToReceived()
        {
            // Arrange
            var check = CreateTestCheck();
            var depositId = Ulid.NewUlid();
            check.DepositToBank(depositId, DateTime.Today);
            check.ClearDomainEvents();

            // Act
            check.RemoveFromDeposit("Test reason");

            // Assert
            Assert.Equal(CheckStatus.Received, check.Status);
            Assert.Null(check.DepositId);
            
            var domainEvents = check.GetDomainEvents();
            Assert.Equal(2, domainEvents.Count);
            Assert.Contains(domainEvents, e => e is CheckStatusChangedEvent);
            Assert.Contains(domainEvents, e => e is CheckRemovedFromDepositEvent);
        }

        [Fact]
        public void Check_RemoveFromDeposit_NotDeposited_ShouldThrowException()
        {
            // Arrange
            var check = CreateTestCheck();

            // Act & Assert
            var exception = Assert.Throws<InvalidOperationException>(() => 
                check.RemoveFromDeposit("Test reason"));
            
            Assert.Contains("not deposited", exception.Message);
        }

        [Fact]
        public void Check_MarkAsCollected_ValidCollection_ShouldUpdateStatus()
        {
            // Arrange
            var check = CreateTestCheck();
            var depositId = Ulid.NewUlid();
            var collectionId = Ulid.NewUlid();
            check.DepositToBank(depositId, DateTime.Today);
            check.ClearDomainEvents();

            // Act
            check.MarkAsCollected(collectionId, DateTime.Today);

            // Assert
            Assert.Equal(CheckStatus.Cleared, check.Status);
            
            var domainEvents = check.GetDomainEvents();
            Assert.Equal(2, domainEvents.Count);
            Assert.Contains(domainEvents, e => e is CheckStatusChangedEvent);
            Assert.Contains(domainEvents, e => e is CheckCollectedEvent);
        }

        [Fact]
        public void Check_MarkAsCollected_NotDeposited_ShouldThrowException()
        {
            // Arrange
            var check = CreateTestCheck();
            var collectionId = Ulid.NewUlid();

            // Act & Assert
            var exception = Assert.Throws<InvalidOperationException>(() => 
                check.MarkAsCollected(collectionId, DateTime.Today));
            
            Assert.Contains("must be deposited before collection", exception.Message);
        }

        [Fact]
        public void Check_MarkAsRejected_ValidRejection_ShouldUpdateStatus()
        {
            // Arrange
            var check = CreateTestCheck();
            var depositId = Ulid.NewUlid();
            var rejectId = Ulid.NewUlid();
            check.DepositToBank(depositId, DateTime.Today);
            check.ClearDomainEvents();

            // Act
            check.MarkAsRejected(rejectId, DateTime.Today, "Insufficient funds");

            // Assert
            Assert.Equal(CheckStatus.Rejected, check.Status);
            
            var domainEvents = check.GetDomainEvents();
            Assert.Equal(2, domainEvents.Count);
            Assert.Contains(domainEvents, e => e is CheckStatusChangedEvent);
            Assert.Contains(domainEvents, e => e is CheckRejectedEvent);
        }

        [Theory]
        [InlineData("")]
        [InlineData(null)]
        public void Check_MarkAsRejected_EmptyReason_ShouldThrowException(string reason)
        {
            // Arrange
            var check = CreateTestCheck();
            var depositId = Ulid.NewUlid();
            var rejectId = Ulid.NewUlid();
            check.DepositToBank(depositId, DateTime.Today);

            // Act & Assert
            var exception = Assert.Throws<ArgumentException>(() => 
                check.MarkAsRejected(rejectId, DateTime.Today, reason));
            
            Assert.Contains("Rejection reason is required", exception.Message);
        }

        [Fact]
        public void Check_CanBeModified_ShouldReturnCorrectValues()
        {
            // Arrange
            var check = CreateTestCheck();

            // Assert - Initial status should allow modification
            Assert.True(check.CanBeModified());

            // Deposit and check again
            check.DepositToBank(Ulid.NewUlid(), DateTime.Today);
            Assert.True(check.CanBeModified());

            // Collect and check again
            check.MarkAsCollected(Ulid.NewUlid(), DateTime.Today);
            Assert.False(check.CanBeModified()); // Final status
        }

        [Fact]
        public void Check_IsInDeposit_ShouldReturnCorrectValues()
        {
            // Arrange
            var check = CreateTestCheck();

            // Assert - Initially not in deposit
            Assert.False(check.IsInDeposit());

            // Deposit and check
            check.DepositToBank(Ulid.NewUlid(), DateTime.Today);
            Assert.True(check.IsInDeposit());

            // Collect and check
            check.MarkAsCollected(Ulid.NewUlid(), DateTime.Today);
            Assert.False(check.IsInDeposit()); // No longer just deposited
        }

        [Fact]
        public void Check_ClearDomainEvents_ShouldRemoveAllEvents()
        {
            // Arrange
            var check = CreateTestCheck();
            check.DepositToBank(Ulid.NewUlid(), DateTime.Today);

            // Act
            Assert.NotEmpty(check.GetDomainEvents());
            check.ClearDomainEvents();

            // Assert
            Assert.Empty(check.GetDomainEvents());
        }
    }
}
