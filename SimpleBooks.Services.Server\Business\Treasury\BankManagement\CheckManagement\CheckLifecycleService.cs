using Microsoft.EntityFrameworkCore;
using SimpleBooks.Models.Domain.Treasury.CheckManagement;
using SimpleBooks.Models.Model.Treasury.BankManagement.CheckManagement;
using SimpleBooks.Repositories.Core.IFactory;
using GMCadiomCore.Models.ResultPattern;

namespace SimpleBooks.Services.Server.Business.Treasury.BankManagement.CheckManagement
{
    public class CheckLifecycleService : ICheckLifecycleService
    {
        private readonly IUnitOfWork _unitOfWork;

        public CheckLifecycleService(IUnitOfWork unitOfWork)
        {
            _unitOfWork = unitOfWork;
        }

        public async Task<ServiceResult<List<Check>>> DepositChecksAsync(List<Ulid> checkIds, CheckDepositModel deposit)
        {
            try
            {
                // Load checks with their status histories
                var repositorySpecs = new RepositorySpecifications<CheckTreasuryVoucherModel>
                {
                    Includes = x => x.Include(c => c.CheckStatusHistories),
                    SearchValue = x => checkIds.Contains(x.Id) && x.CheckStatusId == CheckStatusEnumeration.Received.Value,
                    IsTackable = true
                };

                var checkModels = await _unitOfWork.CheckTreasuryVoucher.GetAllAsync(repositorySpecs);
                
                if (!checkModels.Any())
                    return ServiceResult<List<Check>>.Failure("No valid checks found for deposit");

                // Convert to domain entities
                var checks = checkModels.Select(Check.FromModel).ToList();

                // Validate bulk deposit
                var bulkValidation = CheckBusinessRules.ValidateBulkDeposit(checks);
                if (!bulkValidation.IsValid)
                    return ServiceResult<List<Check>>.Failure(bulkValidation.GetErrorMessage());

                // Validate each check individually
                foreach (var check in checks)
                {
                    var validation = CheckBusinessRules.ValidateDeposit(check, deposit.Id);
                    if (!validation.IsValid)
                        return ServiceResult<List<Check>>.Failure($"Check {check.CheckNumber}: {validation.GetErrorMessage()}");
                }

                // Perform deposit operation
                var statusHistories = new List<CheckStatusHistoryModel>();
                
                foreach (var check in checks)
                {
                    check.DepositToBank(deposit.Id, deposit.DepositDate, $"Check deposited on {deposit.DepositDate:dd/MM/yyyy}");
                    
                    // Process domain events to create status history
                    foreach (var domainEvent in check.GetDomainEvents())
                    {
                        if (domainEvent is CheckStatusChangedEvent statusEvent)
                        {
                            statusHistories.Add(new CheckStatusHistoryModel
                            {
                                Id = Ulid.NewUlid(),
                                TransactionDate = deposit.DepositDate,
                                Note = statusEvent.Note,
                                CheckStatusFromId = statusEvent.FromStatus.Value,
                                CheckStatusToId = statusEvent.ToStatus.Value,
                                CheckTreasuryVoucherId = check.Id,
                                CheckDepositId = statusEvent.DepositId
                            });
                        }
                    }
                    
                    check.ClearDomainEvents();
                }

                // Update check models
                foreach (var check in checks)
                {
                    var model = checkModels.First(m => m.Id == check.Id);
                    model.CheckStatusId = check.Status.Value;
                    model.CheckDepositId = check.DepositId;
                }

                // Add status histories
                foreach (var history in statusHistories)
                {
                    await _unitOfWork.CheckStatusHistory.AddAsync(history);
                }

                // Update deposit with checks and histories
                deposit.CheckTreasuryVouchers = checkModels;
                deposit.CheckStatusHistories = statusHistories;

                return ServiceResult<List<Check>>.Success(checks, "Checks deposited successfully");
            }
            catch (Exception ex)
            {
                return ServiceResult<List<Check>>.Failure($"Failed to deposit checks: {ex.Message}");
            }
        }

        public async Task<ServiceResult<Check>> RemoveCheckFromDepositAsync(Ulid checkId, Ulid depositId, string reason = "")
        {
            try
            {
                // Load check with status histories
                var repositorySpecs = new RepositorySpecifications<CheckTreasuryVoucherModel>
                {
                    Includes = x => x.Include(c => c.CheckStatusHistories),
                    SearchValue = x => x.Id == checkId && x.CheckDepositId == depositId,
                    IsTackable = true
                };

                var checkModel = await _unitOfWork.CheckTreasuryVoucher.GetAsync(repositorySpecs);
                if (checkModel == null)
                    return ServiceResult<Check>.Failure("Check not found or not in specified deposit");

                var check = Check.FromModel(checkModel);

                // Validate removal
                var validation = CheckBusinessRules.ValidateRemovalFromDeposit(check);
                if (!validation.IsValid)
                    return ServiceResult<Check>.Failure(validation.GetErrorMessage());

                // Perform removal
                check.RemoveFromDeposit(reason);

                // Update model
                checkModel.CheckStatusId = check.Status.Value;
                checkModel.CheckDepositId = null;

                // Remove the "Deposited" status history entry
                var depositedHistory = checkModel.CheckStatusHistories
                    .Where(h => h.CheckStatusToId == CheckStatusEnumeration.Deposited.Value && h.CheckDepositId == depositId)
                    .FirstOrDefault();

                if (depositedHistory != null)
                {
                    await _unitOfWork.CheckStatusHistory.RemoveAsync(depositedHistory);
                }

                check.ClearDomainEvents();

                return ServiceResult<Check>.Success(check, "Check removed from deposit successfully");
            }
            catch (Exception ex)
            {
                return ServiceResult<Check>.Failure($"Failed to remove check from deposit: {ex.Message}");
            }
        }

        public async Task<ServiceResult<Check>> CollectCheckAsync(Ulid checkId, CheckCollectionModel collection)
        {
            try
            {
                var repositorySpecs = new RepositorySpecifications<CheckTreasuryVoucherModel>
                {
                    Includes = x => x.Include(c => c.CheckStatusHistories),
                    SearchValue = x => x.Id == checkId,
                    IsTackable = true
                };

                var checkModel = await _unitOfWork.CheckTreasuryVoucher.GetAsync(repositorySpecs);
                if (checkModel == null)
                    return ServiceResult<Check>.Failure("Check not found");

                var check = Check.FromModel(checkModel);

                // Validate collection
                var validation = CheckBusinessRules.ValidateCollection(check, collection.Id);
                if (!validation.IsValid)
                    return ServiceResult<Check>.Failure(validation.GetErrorMessage());

                // Perform collection
                check.MarkAsCollected(collection.Id, collection.CollectionDate);

                // Update model
                checkModel.CheckStatusId = check.Status.Value;

                // Create status history
                var statusHistory = new CheckStatusHistoryModel
                {
                    Id = Ulid.NewUlid(),
                    TransactionDate = collection.CollectionDate,
                    Note = $"Check collected on {collection.CollectionDate:dd/MM/yyyy}",
                    CheckStatusFromId = CheckStatusEnumeration.Deposited.Value,
                    CheckStatusToId = CheckStatusEnumeration.Cleared.Value,
                    CheckTreasuryVoucherId = check.Id,
                    CheckDepositId = check.DepositId,
                    CheckCollectionId = collection.Id
                };

                await _unitOfWork.CheckStatusHistory.AddAsync(statusHistory);
                await _unitOfWork.CheckCollection.AddAsync(collection);

                check.ClearDomainEvents();

                return ServiceResult<Check>.Success(check, "Check collected successfully");
            }
            catch (Exception ex)
            {
                return ServiceResult<Check>.Failure($"Failed to collect check: {ex.Message}");
            }
        }

        public async Task<ServiceResult<Check>> RejectCheckAsync(Ulid checkId, CheckRejectModel rejection)
        {
            try
            {
                var repositorySpecs = new RepositorySpecifications<CheckTreasuryVoucherModel>
                {
                    Includes = x => x.Include(c => c.CheckStatusHistories),
                    SearchValue = x => x.Id == checkId,
                    IsTackable = true
                };

                var checkModel = await _unitOfWork.CheckTreasuryVoucher.GetAsync(repositorySpecs);
                if (checkModel == null)
                    return ServiceResult<Check>.Failure("Check not found");

                var check = Check.FromModel(checkModel);

                // Validate rejection
                var reason = rejection.RefranceNumber ?? "No reason provided";
                var validation = CheckBusinessRules.ValidateRejection(check, rejection.Id, reason);
                if (!validation.IsValid)
                    return ServiceResult<Check>.Failure(validation.GetErrorMessage());

                // Perform rejection
                check.MarkAsRejected(rejection.Id, rejection.RejectDate, reason);

                // Update model
                checkModel.CheckStatusId = check.Status.Value;

                // Create status history
                var statusHistory = new CheckStatusHistoryModel
                {
                    Id = Ulid.NewUlid(),
                    TransactionDate = rejection.RejectDate,
                    Note = $"Check rejected on {rejection.RejectDate:dd/MM/yyyy}: {reason}",
                    CheckStatusFromId = CheckStatusEnumeration.Deposited.Value,
                    CheckStatusToId = CheckStatusEnumeration.Rejected.Value,
                    CheckTreasuryVoucherId = check.Id,
                    CheckDepositId = check.DepositId,
                    CheckRejectId = rejection.Id
                };

                await _unitOfWork.CheckStatusHistory.AddAsync(statusHistory);
                await _unitOfWork.CheckReject.AddAsync(rejection);

                check.ClearDomainEvents();

                return ServiceResult<Check>.Success(check, "Check rejected successfully");
            }
            catch (Exception ex)
            {
                return ServiceResult<Check>.Failure($"Failed to reject check: {ex.Message}");
            }
        }

        public async Task<ServiceResult<Check>> GetCheckAsync(Ulid checkId)
        {
            try
            {
                var repositorySpecs = new RepositorySpecifications<CheckTreasuryVoucherModel>
                {
                    Includes = x => x.Include(c => c.CheckStatusHistories),
                    SearchValue = x => x.Id == checkId
                };

                var checkModel = await _unitOfWork.CheckTreasuryVoucher.GetAsync(repositorySpecs);
                if (checkModel == null)
                    return ServiceResult<Check>.Failure("Check not found");

                var check = Check.FromModel(checkModel);
                return ServiceResult<Check>.Success(check);
            }
            catch (Exception ex)
            {
                return ServiceResult<Check>.Failure($"Failed to get check: {ex.Message}");
            }
        }

        public async Task<ServiceResult<List<Check>>> GetChecksInDepositAsync(Ulid depositId)
        {
            try
            {
                var repositorySpecs = new RepositorySpecifications<CheckTreasuryVoucherModel>
                {
                    Includes = x => x.Include(c => c.CheckStatusHistories),
                    SearchValue = x => x.CheckDepositId == depositId
                };

                var checkModels = await _unitOfWork.CheckTreasuryVoucher.GetAllAsync(repositorySpecs);
                var checks = checkModels.Select(Check.FromModel).ToList();

                return ServiceResult<List<Check>>.Success(checks);
            }
            catch (Exception ex)
            {
                return ServiceResult<List<Check>>.Failure($"Failed to get checks in deposit: {ex.Message}");
            }
        }
    }
}
