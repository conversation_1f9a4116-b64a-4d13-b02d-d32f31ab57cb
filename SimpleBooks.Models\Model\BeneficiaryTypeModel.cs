﻿namespace SimpleBooks.Models.Model
{
    [Table("BeneficiaryType")]
    [TypeDescriptionProvider(typeof(BaseTypeDescriptionProvider<BeneficiaryTypeModel>))]
    public class BeneficiaryTypeModel : BaseWithoutTrackingModel
    {
        [CustomRequired]
        [DisplayName("Beneficiary Type Name")]
        public string BeneficiaryTypeName { get; set; }

        [DisplayName("Cash Treasury Vouchers")]
        public virtual ICollection<CashTreasuryVoucherModel> CashTreasuryVouchers { get; set; } = new List<CashTreasuryVoucherModel>();
        [DisplayName("Check Treasury Vouchers")]
        public virtual ICollection<CheckTreasuryVoucherModel> CheckTreasuryVouchers { get; set; } = new List<CheckTreasuryVoucherModel>();
        [DisplayName("Bank Transfer Treasury Vouchers")]
        public virtual ICollection<BankTransferTreasuryVoucherModel> BankTransferTreasuryVouchers { get; set; } = new List<BankTransferTreasuryVoucherModel>();
    }
}
