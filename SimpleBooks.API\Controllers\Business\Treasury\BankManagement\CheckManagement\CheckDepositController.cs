﻿namespace SimpleBooks.API.Controllers.Business.Treasury.BankManagement.CheckManagement
{
    public class CheckDepositController : BaseBusinessController<CheckDepositModel, CheckDepositModel, CreateCheckDepositViewModel, UpdateCheckDepositViewModel>
    {
        private readonly ICheckDepositService _checkDepositService;

        public CheckDepositController(ICheckDepositService checkDepositService) : base(checkDepositService)
        {
            _checkDepositService = checkDepositService;
        }
    }
}
