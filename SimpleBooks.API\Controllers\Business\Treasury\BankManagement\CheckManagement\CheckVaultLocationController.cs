﻿namespace SimpleBooks.API.Controllers.Business.Treasury.BankManagement.CheckManagement
{
    public class CheckVaultLocationController : BaseBusinessController<CheckVaultLocationModel, CheckVaultLocationModel, CreateCheckVaultLocationViewModel, UpdateCheckVaultLocationViewModel>
    {
        private readonly ICheckVaultLocationService _checkVaultLocationService;

        public CheckVaultLocationController(ICheckVaultLocationService checkVaultLocationService) : base(checkVaultLocationService)
        {
            _checkVaultLocationService = checkVaultLocationService;
        }
    }
}
