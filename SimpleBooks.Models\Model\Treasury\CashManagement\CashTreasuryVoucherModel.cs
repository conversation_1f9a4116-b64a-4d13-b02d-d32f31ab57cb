﻿namespace SimpleBooks.Models.Model.Treasury.CashManagement
{
    [Table("CashTreasuryVoucher")]
    [TypeDescriptionProvider(typeof(BaseTypeDescriptionProvider<CashTreasuryVoucherModel>))]
    public partial class CashTreasuryVoucherModel : TreasuryVoucherModel
    {
        [CustomRequired]
        [DisplayName("Drawer")]
        public Ulid DrawerId { get; set; }
        public virtual DrawerModel? Drawer { get; set; }

        [CustomRequired]
        [DisplayName("Drawer Location")]
        public Ulid DrawerLocationId { get; set; }
        public virtual DrawerLocationModel? DrawerLocation { get; set; }
    }
}
