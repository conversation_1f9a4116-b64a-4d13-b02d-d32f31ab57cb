﻿namespace SimpleBooks.Models.ViewModel.Treasury.CashManagement.DrawerLocation
{
    public class CreateDrawerLocationViewModel : BaseCreateViewModel, IEntityMapper<DrawerLocationModel, CreateDrawerLocationViewModel>
    {
        [CustomRequired]
        [DisplayName("Drawer Account Number")]
        public string DrawerLocationNumber { get; set; }
        [CustomRequired]
        [DisplayName("Drawer Account Currency")]
        public string DrawerLocationCurrency { get; set; }

        public CreateDrawerLocationViewModel ToDto(DrawerLocationModel entity) => entity.ToCreateDto();

        public DrawerLocationModel ToEntity() => DrawerLocationMapper.ToEntity(this);
    }
}
