﻿namespace SimpleBooks.API.Controllers.Business.Treasury.BankManagement.CheckManagement
{
    public class CheckCollectionController : BaseBusinessController<CheckCollectionModel, CheckCollectionModel, CreateCheckCollectionViewModel, UpdateCheckCollectionViewModel>
    {
        private readonly ICheckCollectionService _checkCollectionService;

        public CheckCollectionController(ICheckCollectionService checkCollectionService) : base(checkCollectionService)
        {
            _checkCollectionService = checkCollectionService;
        }
    }
}
