﻿using SimpleBooks.Repositories.Core.IRepository.Treasury.BankManagement.CheckManagement;

namespace SimpleBooks.Services.Server.Business.Treasury.BankManagement.CheckManagement
{
    public class CheckDepositService : SimpleBooksBaseService<CheckDepositModel, CheckDepositModel, CreateCheckDepositViewModel, UpdateCheckDepositViewModel>, ICheckDepositService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IBankService _bankService;
        private readonly IBankAccountService _bankAccountService;
        private readonly ICheckTreasuryVoucherService _checkTreasuryVoucherService;
        private readonly ICheckLifecycleService _checkLifecycleService;

        public CheckDepositService(
            IAuthenticationValidationService authenticationValidationService,
            IUnitOfWork unitOfWork,
            IBankService bankService,
            IBankAccountService bankAccountService,
            ICheckTreasuryVoucherService checkTreasuryVoucherService,
            ICheckLifecycleService checkLifecycleService) : base(authenticationValidationService, unitOfWork.CheckDeposit)
        {
            _unitOfWork = unitOfWork;
            _bankService = bankService;
            _bankAccountService = bankAccountService;
            _checkTreasuryVoucherService = checkTreasuryVoucherService;
            _checkLifecycleService = checkLifecycleService;
        }

        protected override Func<IQueryable<CheckDepositModel>, IIncludableQueryable<CheckDepositModel, object>>? Includes =>
            x => x
            .Include(xx => xx.CheckTreasuryVouchers)
            .Include(xx => xx.CheckStatusHistories);

        public override async Task<ServiceResult<CheckDepositModel?>> AddAsync(CheckDepositModel model)
        {
            try
            {
                // Use the new CheckLifecycleService for proper domain-driven deposit handling
                List<Ulid> checkIds = model.CheckTreasuryVouchers.Select(x => x.Id).ToList();

                var depositResult = await _checkLifecycleService.DepositChecksAsync(checkIds, model);
                if (!depositResult.IsSuccess)
                {
                    return ServiceResult<CheckDepositModel?>.Failure(depositResult.Message);
                }

                // The CheckLifecycleService has already handled the check updates and status histories
                // Now we just need to save the deposit itself
                return await base.AddAsync(model);
            }
            catch (Exception ex)
            {
                return ServiceResult<CheckDepositModel?>.Failure($"Failed to create deposit: {ex.Message}");
            }
        }

        /// <summary>
        /// Remove a check from deposit with proper validation and audit trail
        /// </summary>
        public async Task<ServiceResult<bool>> RemoveCheckFromDepositAsync(Ulid checkId, Ulid depositId, string reason = "")
        {
            try
            {
                var result = await _checkLifecycleService.RemoveCheckFromDepositAsync(checkId, depositId, reason);
                return ServiceResult<bool>.Success(result.IsSuccess, result.Message);
            }
            catch (Exception ex)
            {
                return ServiceResult<bool>.Failure($"Failed to remove check from deposit: {ex.Message}");
            }
        }

        /// <summary>
        /// Get deposit summary with check status breakdown
        /// </summary>
        public async Task<ServiceResult<DepositSummary>> GetDepositSummaryAsync(Ulid depositId)
        {
            try
            {
                var summary = await _unitOfWork.CheckLifecycle.GetDepositSummaryAsync(depositId);
                return ServiceResult<DepositSummary>.Success(summary);
            }
            catch (Exception ex)
            {
                return ServiceResult<DepositSummary>.Failure($"Failed to get deposit summary: {ex.Message}");
            }
        }

        /// <summary>
        /// Get checks that can be removed from deposit (only deposited status)
        /// </summary>
        public async Task<ServiceResult<List<CheckTreasuryVoucherModel>>> GetRemovableChecksAsync(Ulid depositId)
        {
            try
            {
                var checks = await _unitOfWork.CheckLifecycle.GetChecksRemovableFromDepositAsync(depositId);
                return ServiceResult<List<CheckTreasuryVoucherModel>>.Success(checks);
            }
            catch (Exception ex)
            {
                return ServiceResult<List<CheckTreasuryVoucherModel>>.Failure($"Failed to get removable checks: {ex.Message}");
            }
        }

        public override async Task<ServiceResult<CheckDepositModel?>> UpdateAsync(CheckDepositModel model)
        {
            List<Ulid> ids = model.CheckTreasuryVouchers.Select(x => x.Id).ToList();
            List<CheckStatusHistoryModel> checkStatusHistories = new List<CheckStatusHistoryModel>();

            RepositorySpecifications<CheckTreasuryVoucherModel> repositorySpecifications = new RepositorySpecifications<CheckTreasuryVoucherModel>
            {
                Includes = x => x.Include(xx => xx.CheckStatusHistories),
                SearchValue = x => ids.Contains(x.Id) && x.CheckStatusId == CheckStatusEnumeration.Received.Value,
                IsTackable = true,
            };
            var selectedCheckTreasuryVouchers = await _unitOfWork.CheckTreasuryVoucher.GetAllAsync(repositorySpecifications);

            foreach (var check in selectedCheckTreasuryVouchers)
            {
                check.CheckStatusId = CheckStatusEnumeration.Deposited.Value;
                check.CheckDepositId = model.Id;

                checkStatusHistories.Add(new CheckStatusHistoryModel
                {
                    TransactionDate = model.DepositDate,
                    Note = $"Check deposited on {model.DepositDate:dd/MM/yyyy}",
                    CheckStatusFromId = CheckStatusEnumeration.Received.Value,
                    CheckStatusToId = CheckStatusEnumeration.Deposited.Value,
                    CheckTreasuryVoucherId = check.Id,
                    CheckDepositId = model.Id
                });
            }

            model.CheckStatusHistories = checkStatusHistories;
            model.CheckTreasuryVouchers = selectedCheckTreasuryVouchers;

            return await base.UpdateAsync(model);
        }

        public async Task<ServiceResult<IEnumerable<IdAndName>>> SelectiveBankListAsync() => await _bankService.GetSelectListAsync();
        public async Task<ServiceResult<IEnumerable<BankAccountModel>>> SelectiveBankAccountListAsync() => await _bankAccountService.GetAllAsync();
        public async Task<ServiceResult<IEnumerable<CheckTreasuryVoucherModel>>> SelectiveCheckTreasuryVouchers() => await _checkTreasuryVoucherService.SelectiveCheckTreasuryVouchersAsync(CheckStatusEnumeration.Received.Value);
    }
}
