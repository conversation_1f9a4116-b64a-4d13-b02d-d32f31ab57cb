﻿namespace SimpleBooks.Models.ViewModel.Treasury.TreasuryVoucher
{
    public class CreateTreasuryVoucherViewModel : BaseCreateViewModel
    {
        [CustomRequired]
        [DisplayName("Transaction Date")]
        [Column(TypeName = "Date"), DataType(DataType.Date)]
        public DateTime TransactionDate { get; set; }
        [CustomRequired]
        [DisplayName("Amount")]
        public decimal Amount { get; set; }
        [CustomRequired]
        [DisplayName("Note")]
        public string Note { get; set; }
        [DisplayName("Refrance Number")]
        public string? RefranceNumber { get; set; }

        [CustomRequired]
        [DisplayName("Transaction Type")]
        public Ulid TransactionTypeId { get; set; }
        [CustomRequired]
        [DisplayName("Beneficiary Type")]
        public Ulid BeneficiaryTypeId { get; set; }
        [CustomRequired]
        [DisplayName("Beneficiary")]
        public Ulid BeneficiaryId { get; set; }

        [CustomRequired]
        [DisplayName("Treasury Lines")]
        public IList<CreateTreasuryLineViewModel> TreasuryLines { get; set; } = new List<CreateTreasuryLineViewModel>();
    }
}
