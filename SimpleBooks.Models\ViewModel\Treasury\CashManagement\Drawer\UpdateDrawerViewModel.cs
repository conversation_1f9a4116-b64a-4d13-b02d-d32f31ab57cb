﻿namespace SimpleBooks.Models.ViewModel.Treasury.CashManagement.Drawer
{
    public class UpdateDrawerViewModel : BaseUpdateViewModel, IEntityMapper<DrawerModel, UpdateDrawerViewModel>
    {
        [CustomRequired]
        [DisplayN<PERSON>("Drawer Name")]
        public string DrawerName { get; set; }

        [CustomRequired]
        [DisplayName("Drawer Locations")]
        public IList<UpdateDrawerLocationViewModel> DrawerLocations { get; set; } = new List<UpdateDrawerLocationViewModel>();

        public UpdateDrawerViewModel ToDto(DrawerModel entity) => entity.ToUpdateDto();

        public DrawerModel ToEntity() => DrawerMapper.ToEntity(this);
    }
}
